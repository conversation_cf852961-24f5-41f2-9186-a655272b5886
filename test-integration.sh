#!/bin/bash

# BiteBase Intelligence - Integration Test Script
# Tests frontend-backend communication

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_header() {
    echo -e "${YELLOW}$1${NC}"
}

# Test configuration
BACKEND_URL="http://localhost:8000"
FRONTEND_URL="http://localhost:3000"

print_header "🧪 BiteBase Intelligence Integration Tests"
echo ""

# Test 1: Backend Health Check
print_status "Testing backend health endpoint..."
if curl -f -s "$BACKEND_URL/health" > /dev/null; then
    print_success "Backend health check passed"
else
    print_error "Backend health check failed"
    exit 1
fi

# Test 2: Backend API Documentation
print_status "Testing API documentation endpoint..."
if curl -f -s "$BACKEND_URL/docs" > /dev/null; then
    print_success "API documentation accessible"
else
    print_error "API documentation not accessible"
    exit 1
fi

# Test 3: CORS Configuration
print_status "Testing CORS configuration..."
CORS_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" -H "Origin: http://localhost:3000" "$BACKEND_URL/health")
if [ "$CORS_RESPONSE" = "200" ]; then
    print_success "CORS configuration working"
else
    print_error "CORS configuration failed"
    exit 1
fi

# Test 4: Auth endpoints structure
print_status "Testing auth endpoints structure..."
if curl -f -s "$BACKEND_URL/api/v1/auth/login" -X POST -H "Content-Type: application/json" -d '{}' > /dev/null 2>&1; then
    print_success "Auth endpoints accessible"
else
    # This might fail due to validation, but endpoint should be reachable
    if curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/api/v1/auth/login" | grep -q "422\|400"; then
        print_success "Auth endpoints accessible (validation working)"
    else
        print_error "Auth endpoints not accessible"
        exit 1
    fi
fi

# Test 5: Frontend accessibility (if running)
print_status "Testing frontend accessibility..."
if curl -f -s "$FRONTEND_URL" > /dev/null 2>&1; then
    print_success "Frontend accessible"
else
    print_error "Frontend not accessible (may not be started yet)"
fi

print_header "✅ Integration tests completed successfully!"
echo ""
print_success "Backend API: $BACKEND_URL"
print_success "Frontend: $FRONTEND_URL"
print_success "API Docs: $BACKEND_URL/docs"
echo ""