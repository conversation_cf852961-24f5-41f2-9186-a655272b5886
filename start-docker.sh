#!/bin/bash

# BiteBase Intelligence - Docker Startup Script
# This script manages Docker-based deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Configuration
COMPOSE_FILE="docker-compose.yml"
ENVIRONMENT=${1:-"development"}
ENABLE_LOGGING=${2:-"false"}

# Print banner
clear
echo -e "${CYAN}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  ██████╗ ██╗████████╗███████╗██████╗  █████╗ ███████╗███████╗║
║  ██╔══██╗██║╚══██╔══╝██╔════╝██╔══██╗██╔══██╗██╔════╝██╔════╝║
║  ██████╔╝██║   ██║   █████╗  ██████╔╝███████║███████╗█████╗  ║
║  ██╔══██╗██║   ██║   ██╔══╝  ██╔══██╗██╔══██║╚════██║██╔══╝  ║
║  ██████╔╝██║   ██║   ███████╗██████╔╝██║  ██║███████║███████╗║
║  ╚═════╝ ╚═╝   ╚═╝   ╚══════╝╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝║
║                                                              ║
║                    DOCKER DEPLOYMENT                         ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

print_header "🐳 Starting BiteBase Intelligence with Docker..."
print_status "Environment: $ENVIRONMENT"
print_status "Logging enabled: $ENABLE_LOGGING"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_header "📋 Checking Prerequisites..."

if ! command_exists docker; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command_exists docker-compose; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Docker daemon is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker daemon is not running. Please start Docker first."
    exit 1
fi

print_success "All prerequisites are met!"

# Set compose file based on environment
if [ "$ENVIRONMENT" = "production" ]; then
    COMPOSE_FILE="docker-compose.prod.yml"
    print_status "Using production configuration"
fi

# Function to cleanup on exit
cleanup() {
    print_warning "Received shutdown signal..."
    print_status "Stopping Docker services..."
    
    if [ "$ENABLE_LOGGING" = "true" ]; then
        docker-compose -f $COMPOSE_FILE --profile logging down
    else
        docker-compose -f $COMPOSE_FILE down
    fi
    
    print_success "Services stopped. Goodbye!"
    exit 0
}

# Trap SIGINT (Ctrl+C) and SIGTERM
trap cleanup SIGINT SIGTERM

# Build and start services
print_header "🏗️ Building Docker Images..."
if [ "$ENABLE_LOGGING" = "true" ]; then
    docker-compose -f $COMPOSE_FILE --profile logging build --no-cache
else
    docker-compose -f $COMPOSE_FILE build --no-cache
fi

print_header "🚀 Starting Services..."
if [ "$ENABLE_LOGGING" = "true" ]; then
    docker-compose -f $COMPOSE_FILE --profile logging up -d
else
    docker-compose -f $COMPOSE_FILE up -d
fi

# Wait for services to be ready
print_status "Waiting for services to start..."
sleep 10

# Check service health
print_header "🔍 Checking Service Health..."

# Check backend health
print_status "Checking backend health..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        print_success "Backend is healthy"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Backend health check failed"
        exit 1
    fi
    sleep 2
done

# Check frontend health (Docker deployment uses port 80)
print_status "Checking frontend health..."
frontend_port=80
if [ "$ENVIRONMENT" = "development" ]; then
    frontend_port=3000
fi

for i in {1..30}; do
    if curl -s http://localhost:$frontend_port >/dev/null 2>&1; then
        print_success "Frontend is healthy"
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "Frontend health check failed"
        exit 1
    fi
    sleep 2
done

# Check database health
print_status "Checking database health..."
if docker-compose -f $COMPOSE_FILE exec -T postgres pg_isready -U bitebase_user >/dev/null 2>&1; then
    print_success "Database is healthy"
else
    print_warning "Database health check failed, but continuing..."
fi

# Success message
print_header "✅ BiteBase Intelligence Platform is now running!"
echo ""
if [ "$ENVIRONMENT" = "production" ]; then
    print_success "🌐 Frontend: http://localhost"
    print_success "🔧 Backend API: http://localhost:8000"
else
    print_success "🌐 Frontend: http://localhost:$frontend_port"
    print_success "🔧 Backend API: http://localhost:8000"
fi
print_success "📚 API Documentation: http://localhost:8000/docs"
print_success "🗄️ Database: localhost:5432"
print_success "🔄 Redis Cache: localhost:6379"

if [ "$ENABLE_LOGGING" = "true" ]; then
    print_success "📊 Prometheus: http://localhost:9090"
    print_success "📈 Grafana: http://localhost:3001 (admin/admin)"
    print_success "🔍 Elasticsearch: http://localhost:9200"
    print_success "📋 Kibana: http://localhost:5601"
fi

echo ""
print_status "Press Ctrl+C to stop all services"
print_status "Use 'docker-compose logs -f' to view logs"
echo ""

# Show running containers
print_header "📊 Running Containers:"
docker-compose -f $COMPOSE_FILE ps

# Keep script running and monitor services
print_status "Monitoring services... (Press Ctrl+C to stop)"
while true; do
    # Check if any container has stopped
    if ! docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        print_warning "Some containers may have stopped. Checking..."
        docker-compose -f $COMPOSE_FILE ps
    fi
    sleep 30
done
