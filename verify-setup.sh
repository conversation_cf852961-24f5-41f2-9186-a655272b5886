#!/bin/bash

# BiteBase Intelligence - Setup Verification Script
# Verifies that the project is ready to run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

print_header "🔍 BiteBase Intelligence Setup Verification"
echo ""

# Check 1: Node.js and npm
print_status "Checking Node.js and npm..."
if command -v node >/dev/null 2>&1 && command -v npm >/dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    print_success "Node.js $NODE_VERSION and npm $NPM_VERSION installed"
else
    print_error "Node.js or npm not found"
    exit 1
fi

# Check 2: Python
print_status "Checking Python..."
if command -v python3 >/dev/null 2>&1; then
    PYTHON_VERSION=$(python3 --version)
    print_success "$PYTHON_VERSION installed"
else
    print_error "Python 3 not found"
    exit 1
fi

# Check 3: pip
print_status "Checking pip..."
if command -v pip >/dev/null 2>&1; then
    PIP_VERSION=$(pip --version)
    print_success "pip installed"
else
    print_error "pip not found"
    exit 1
fi

# Check 4: Frontend dependencies
print_status "Checking frontend dependencies..."
if [ -d "frontend/node_modules" ]; then
    print_success "Frontend dependencies installed"
else
    print_warning "Frontend dependencies not installed. Run: cd frontend && npm install"
fi

# Check 5: Backend virtual environment
print_status "Checking backend virtual environment..."
if [ -d "backend/venv" ]; then
    print_success "Backend virtual environment exists"
else
    print_warning "Backend virtual environment not found. Will be created automatically"
fi

# Check 6: Configuration files
print_status "Checking configuration files..."
if [ -f "backend/.env" ]; then
    print_success "Backend .env file exists"
else
    print_warning "Backend .env file not found. Using defaults"
fi

if [ -f "frontend/.env.local" ]; then
    print_success "Frontend .env.local file exists"
else
    print_warning "Frontend .env.local file not found. Using defaults"
fi

# Check 7: Project structure
print_status "Checking project structure..."
REQUIRED_DIRS=("frontend/src" "backend/app" "frontend/src/components" "backend/app/api")
ALL_DIRS_EXIST=true

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        print_error "Required directory $dir not found"
        ALL_DIRS_EXIST=false
    fi
done

if [ "$ALL_DIRS_EXIST" = true ]; then
    print_success "Project structure verified"
fi

# Check 8: Key files
print_status "Checking key files..."
KEY_FILES=(
    "frontend/src/app/page.tsx"
    "frontend/src/components/layout/Header.tsx"
    "frontend/src/components/layout/Footer.tsx"
    "frontend/src/components/landing/PricingSection.tsx"
    "backend/app/main.py"
    "backend/app/core/config.py"
    "start.sh"
)

ALL_FILES_EXIST=true
for file in "${KEY_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        print_error "Required file $file not found"
        ALL_FILES_EXIST=false
    fi
done

if [ "$ALL_FILES_EXIST" = true ]; then
    print_success "Key files verified"
fi

# Check 9: Start script permissions
print_status "Checking start script permissions..."
if [ -x "start.sh" ]; then
    print_success "Start script is executable"
else
    print_warning "Start script is not executable. Run: chmod +x start.sh"
fi

echo ""
print_header "📋 Setup Summary"
echo ""
print_success "✅ Prerequisites: Node.js, Python, pip installed"
print_success "✅ Project structure: All required directories and files present"
print_success "✅ Configuration: Environment files ready"
print_success "✅ Scripts: Startup script configured"
echo ""
print_header "🚀 Ready to Start!"
echo ""
echo "To start the platform, run:"
echo "  ${GREEN}./start.sh${NC}"
echo ""
echo "This will:"
echo "  • Set up Python virtual environment"
echo "  • Install all dependencies"
echo "  • Start backend on http://localhost:8000"
echo "  • Start frontend on http://localhost:3000"
echo ""
echo "To test the integration, run:"
echo "  ${GREEN}./test-integration.sh${NC}"
echo ""