name: Production Deployment Pipeline

on:
  push:
    branches: [main]
    tags: ['v*']
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # Security and Quality Checks
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Run Bandit security linter
        run: |
          pip install bandit[toml]
          bandit -r backend/ -f json -o bandit-report.json || true

      - name: Run npm audit
        working-directory: frontend
        run: |
          npm audit --audit-level=high --json > npm-audit.json || true

  # Frontend Tests and Build
  frontend-test:
    name: Frontend Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        working-directory: frontend
        run: npm ci

      - name: Run linting
        working-directory: frontend
        run: npm run lint

      - name: Run type checking
        working-directory: frontend
        run: npm run type-check

      - name: Run unit tests
        working-directory: frontend
        run: npm run test:coverage

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: frontend/coverage/lcov.info
          flags: frontend

      - name: Build frontend
        working-directory: frontend
        run: npm run build
        env:
          NODE_ENV: production

      - name: Upload frontend build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: frontend-build
          path: frontend/.next/

  # Backend Tests and Build
  backend-test:
    name: Backend Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: latest
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v3
        with:
          path: backend/.venv
          key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}

      - name: Install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        working-directory: backend
        run: poetry install --no-interaction --no-root

      - name: Install project
        working-directory: backend
        run: poetry install --no-interaction

      - name: Run linting
        working-directory: backend
        run: |
          poetry run flake8 app/
          poetry run black --check app/
          poetry run isort --check-only app/

      - name: Run type checking
        working-directory: backend
        run: poetry run mypy app/

      - name: Run tests
        working-directory: backend
        run: poetry run pytest --cov=app --cov-report=xml --cov-report=html
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379/0
          ENVIRONMENT: test

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: backend/coverage.xml
          flags: backend

  # Build and Push Docker Images
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [security-scan, frontend-test, backend-test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    outputs:
      frontend-image: ${{ steps.meta-frontend.outputs.tags }}
      backend-image: ${{ steps.meta-backend.outputs.tags }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Frontend Image
      - name: Extract metadata for frontend
        id: meta-frontend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-frontend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: frontend
          file: frontend/Dockerfile.prod
          push: true
          tags: ${{ steps.meta-frontend.outputs.tags }}
          labels: ${{ steps.meta-frontend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      # Backend Image
      - name: Extract metadata for backend
        id: meta-backend
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: backend
          file: backend/Dockerfile.prod
          push: true
          tags: ${{ steps.meta-backend.outputs.tags }}
          labels: ${{ steps.meta-backend.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-images]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://staging.bitebase.ai
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name bitebase-staging --region us-west-2

      - name: Deploy to staging
        run: |
          # Update image tags in Kubernetes manifests
          sed -i "s|IMAGE_TAG|${{ github.sha }}|g" k8s/staging/*.yaml
          
          # Apply Kubernetes manifests
          kubectl apply -f k8s/staging/namespace.yaml
          kubectl apply -f k8s/staging/configmap.yaml
          kubectl apply -f k8s/staging/secrets.yaml
          kubectl apply -f k8s/staging/database.yaml
          kubectl apply -f k8s/staging/redis.yaml
          kubectl apply -f k8s/staging/backend.yaml
          kubectl apply -f k8s/staging/frontend.yaml
          kubectl apply -f k8s/staging/ingress.yaml
          
          # Wait for deployment to complete
          kubectl rollout status deployment/backend -n bitebase-staging --timeout=600s
          kubectl rollout status deployment/frontend -n bitebase-staging --timeout=600s

      - name: Run smoke tests
        run: |
          # Wait for services to be ready
          sleep 30
          
          # Run basic health checks
          curl -f https://staging-api.bitebase.ai/health || exit 1
          curl -f https://staging.bitebase.ai || exit 1

  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install test dependencies
        working-directory: tests/integration
        run: npm ci

      - name: Run integration tests
        working-directory: tests/integration
        run: npm test
        env:
          BASE_URL: https://staging.bitebase.ai
          API_URL: https://staging-api.bitebase.ai
          TEST_USER_EMAIL: ${{ secrets.TEST_USER_EMAIL }}
          TEST_USER_PASSWORD: ${{ secrets.TEST_USER_PASSWORD }}

  # Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [integration-tests]
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
    environment:
      name: production
      url: https://app.bitebase.ai
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Update kubeconfig
        run: aws eks update-kubeconfig --name bitebase-production --region us-west-2

      - name: Create backup
        run: |
          # Create database backup before deployment
          kubectl create job backup-$(date +%Y%m%d-%H%M%S) \
            --from=cronjob/database-backup -n bitebase-production

      - name: Deploy to production
        run: |
          # Update image tags in Kubernetes manifests
          sed -i "s|IMAGE_TAG|${{ github.ref_name }}|g" k8s/production/*.yaml
          
          # Apply Kubernetes manifests with rolling update
          kubectl apply -f k8s/production/namespace.yaml
          kubectl apply -f k8s/production/configmap.yaml
          kubectl apply -f k8s/production/secrets.yaml
          kubectl apply -f k8s/production/database.yaml
          kubectl apply -f k8s/production/redis.yaml
          kubectl apply -f k8s/production/backend.yaml
          kubectl apply -f k8s/production/frontend.yaml
          kubectl apply -f k8s/production/ingress.yaml
          
          # Wait for deployment to complete
          kubectl rollout status deployment/backend -n bitebase-production --timeout=900s
          kubectl rollout status deployment/frontend -n bitebase-production --timeout=900s

      - name: Run production health checks
        run: |
          # Wait for services to be ready
          sleep 60
          
          # Run comprehensive health checks
          curl -f https://api.bitebase.ai/health || exit 1
          curl -f https://app.bitebase.ai || exit 1
          
          # Check database connectivity
          kubectl exec -n bitebase-production deployment/backend -- \
            python -c "from app.database import engine; engine.execute('SELECT 1')"

      - name: Notify deployment success
        if: success()
        run: |
          # Send notification to Slack/Teams
          curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"🚀 Production deployment successful: ${{ github.ref_name }}"}' \
            ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Rollback on failure
        if: failure()
        run: |
          # Rollback to previous version
          kubectl rollout undo deployment/backend -n bitebase-production
          kubectl rollout undo deployment/frontend -n bitebase-production
          
          # Notify failure
          curl -X POST -H 'Content-type: application/json' \
            --data '{"text":"❌ Production deployment failed: ${{ github.ref_name }}. Rolled back to previous version."}' \
            ${{ secrets.SLACK_WEBHOOK_URL }}

  # Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.event_name == 'push' && startsWith(github.ref, 'refs/tags/v')
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run load tests
        run: |
          # Install k6
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
          # Run performance tests
          k6 run tests/performance/load-test.js \
            --env BASE_URL=https://api.bitebase.ai \
            --env API_KEY=${{ secrets.PERFORMANCE_TEST_API_KEY }}

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-results.json
