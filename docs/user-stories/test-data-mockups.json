{"testUsers": [{"id": "user-001", "email": "<EMAIL>", "password": "Test@1234!", "role": "restaurant_owner", "profile": {"firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "******-123-4567", "timezone": "America/Los_Angeles", "language": "en", "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=john"}, "restaurants": ["rest-001", "rest-002"], "permissions": ["all"], "subscription": {"plan": "professional", "status": "active", "billingCycle": "monthly", "nextBillingDate": "2024-02-01"}, "mfaEnabled": true, "lastLogin": "2024-01-15T09:30:00Z", "createdAt": "2023-06-15T14:20:00Z"}, {"id": "user-002", "email": "<EMAIL>", "password": "Secure#5678", "role": "manager", "profile": {"firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "******-987-6543", "timezone": "America/New_York", "language": "en", "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=sarah"}, "restaurants": ["rest-003"], "permissions": ["view_analytics", "manage_menu", "manage_staff"], "subscription": {"plan": "starter", "status": "active", "billingCycle": "yearly", "nextBillingDate": "2024-12-15"}, "mfaEnabled": false, "lastLogin": "2024-01-14T14:45:00Z", "createdAt": "2023-08-20T10:30:00Z"}, {"id": "user-003", "email": "<EMAIL>", "password": "Franchise$999", "role": "franchise_owner", "profile": {"firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "******-555-5555", "timezone": "America/Chicago", "language": "en", "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=michael"}, "restaurants": ["rest-004", "rest-005", "rest-006", "rest-007", "rest-008"], "permissions": ["all"], "subscription": {"plan": "enterprise", "status": "active", "billingCycle": "yearly", "nextBillingDate": "2024-06-30", "customFeatures": ["white_label", "api_access", "dedicated_support"]}, "mfaEnabled": true, "lastLogin": "2024-01-15T16:20:00Z", "createdAt": "2023-01-10T09:00:00Z"}, {"id": "user-004", "email": "<EMAIL>", "password": "Admin@2024!", "role": "platform_admin", "profile": {"firstName": "Admin", "lastName": "User", "phone": "******-000-0000", "timezone": "UTC", "language": "en", "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=admin"}, "restaurants": [], "permissions": ["platform_admin"], "subscription": null, "mfaEnabled": true, "lastLogin": "2024-01-15T18:00:00Z", "createdAt": "2023-01-01T00:00:00Z"}, {"id": "user-005", "email": "<EMAIL>", "password": "Thai#2024", "role": "restaurant_owner", "profile": {"firstName": "สมชาย", "lastName": "วงศ์สว่าง", "phone": "+66-2-123-4567", "timezone": "Asia/Bangkok", "language": "th", "avatar": "https://api.dicebear.com/7.x/avataaars/svg?seed=somchai"}, "restaurants": ["rest-009"], "permissions": ["all"], "subscription": {"plan": "professional", "status": "active", "billingCycle": "monthly", "nextBillingDate": "2024-02-05"}, "mfaEnabled": false, "lastLogin": "2024-01-15T11:30:00Z", "createdAt": "2023-09-10T08:00:00Z"}], "testRestaurants": [{"id": "rest-001", "name": "Tasty Bites Downtown", "type": "full_service", "cuisine": ["American", "Contemporary"], "address": {"street": "123 Main Street", "city": "Los Angeles", "state": "CA", "zipCode": "90012", "country": "USA", "latitude": 34.0522, "longitude": -118.2437}, "contact": {"phone": "******-123-1111", "email": "<EMAIL>", "website": "https://tastybites.com"}, "operatingHours": {"monday": {"open": "11:00", "close": "22:00"}, "tuesday": {"open": "11:00", "close": "22:00"}, "wednesday": {"open": "11:00", "close": "22:00"}, "thursday": {"open": "11:00", "close": "23:00"}, "friday": {"open": "11:00", "close": "23:00"}, "saturday": {"open": "10:00", "close": "23:00"}, "sunday": {"open": "10:00", "close": "21:00"}}, "features": ["delivery", "takeout", "dine_in", "reservations", "parking"], "averageRating": 4.5, "totalReviews": 1523, "priceRange": "$$", "capacity": 120, "established": "2019-03-15"}, {"id": "rest-002", "name": "Tasty Bites Westside", "type": "full_service", "cuisine": ["American", "Contemporary"], "address": {"street": "456 Ocean Boulevard", "city": "Santa Monica", "state": "CA", "zipCode": "90401", "country": "USA", "latitude": 34.0195, "longitude": -118.4912}, "contact": {"phone": "******-123-2222", "email": "<EMAIL>", "website": "https://tastybites.com"}, "operatingHours": {"monday": {"open": "11:00", "close": "22:00"}, "tuesday": {"open": "11:00", "close": "22:00"}, "wednesday": {"open": "11:00", "close": "22:00"}, "thursday": {"open": "11:00", "close": "22:00"}, "friday": {"open": "11:00", "close": "23:00"}, "saturday": {"open": "10:00", "close": "23:00"}, "sunday": {"open": "10:00", "close": "22:00"}}, "features": ["delivery", "takeout", "dine_in", "outdoor_seating", "valet_parking"], "averageRating": 4.6, "totalReviews": 987, "priceRange": "$$$", "capacity": 150, "established": "2021-06-20"}, {"id": "rest-003", "name": "Dragon Wok Express", "type": "quick_service", "cuisine": ["Chinese", "Asian Fusion"], "address": {"street": "789 Broadway", "city": "New York", "state": "NY", "zipCode": "10003", "country": "USA", "latitude": 40.7128, "longitude": -74.006}, "contact": {"phone": "******-987-3333", "email": "<EMAIL>", "website": "https://dragonwok.com"}, "operatingHours": {"monday": {"open": "10:30", "close": "22:30"}, "tuesday": {"open": "10:30", "close": "22:30"}, "wednesday": {"open": "10:30", "close": "22:30"}, "thursday": {"open": "10:30", "close": "22:30"}, "friday": {"open": "10:30", "close": "23:00"}, "saturday": {"open": "11:00", "close": "23:00"}, "sunday": {"open": "11:00", "close": "22:00"}}, "features": ["delivery", "takeout", "online_ordering"], "averageRating": 4.2, "totalReviews": 2341, "priceRange": "$", "capacity": 45, "established": "2018-11-10"}, {"id": "rest-004", "name": "Burger Empire - Chicago Downtown", "type": "quick_service", "cuisine": ["American", "Burgers"], "address": {"street": "100 N Michigan Ave", "city": "Chicago", "state": "IL", "zipCode": "60601", "country": "USA", "latitude": 41.8781, "longitude": -87.6298}, "contact": {"phone": "******-555-4444", "email": "<EMAIL>", "website": "https://burgerempire.com"}, "operatingHours": {"monday": {"open": "10:00", "close": "23:00"}, "tuesday": {"open": "10:00", "close": "23:00"}, "wednesday": {"open": "10:00", "close": "23:00"}, "thursday": {"open": "10:00", "close": "23:00"}, "friday": {"open": "10:00", "close": "00:00"}, "saturday": {"open": "10:00", "close": "00:00"}, "sunday": {"open": "10:00", "close": "23:00"}}, "features": ["delivery", "takeout", "dine_in", "drive_thru"], "averageRating": 4.1, "totalReviews": 3456, "priceRange": "$$", "capacity": 80, "established": "2020-02-01"}, {"id": "rest-009", "name": "Thai Delights Bangkok", "type": "full_service", "cuisine": ["Thai", "Authentic Thai"], "address": {"street": "88 Sukhumvit Road", "city": "Bangkok", "state": "Bangkok", "zipCode": "10110", "country": "Thailand", "latitude": 13.7563, "longitude": 100.5018}, "contact": {"phone": "+66-2-123-4567", "email": "<EMAIL>", "website": "https://thaidelights.com"}, "operatingHours": {"monday": {"open": "11:00", "close": "22:00"}, "tuesday": {"open": "11:00", "close": "22:00"}, "wednesday": {"open": "11:00", "close": "22:00"}, "thursday": {"open": "11:00", "close": "22:00"}, "friday": {"open": "11:00", "close": "23:00"}, "saturday": {"open": "11:00", "close": "23:00"}, "sunday": {"open": "11:00", "close": "22:00"}}, "features": ["delivery", "takeout", "dine_in", "reservations", "live_music"], "averageRating": 4.7, "totalReviews": 892, "priceRange": "$$", "capacity": 100, "established": "2017-05-15"}], "testMenuItems": [{"id": "menu-001", "restaurantId": "rest-001", "category": "Appetizers", "name": "<PERSON><PERSON><PERSON>", "description": "Fresh calamari rings, lightly breaded and fried, served with marinara sauce", "price": 12.99, "ingredients": ["calamari", "flour", "spices", "marinara sauce"], "allergens": ["seafood", "gluten"], "nutritionalInfo": {"calories": 350, "protein": 15, "carbs": 25, "fat": 20}, "availability": "always", "preparationTime": 15, "spicyLevel": 0, "vegetarian": false, "vegan": false, "glutenFree": false, "popular": true, "image": "https://example.com/calamari.jpg"}, {"id": "menu-002", "restaurantId": "rest-001", "category": "Main Courses", "name": "Grilled Salmon", "description": "Atlantic salmon grilled to perfection, served with seasonal vegetables", "price": 24.99, "ingredients": ["salmon", "vegetables", "lemon", "herbs"], "allergens": ["fish"], "nutritionalInfo": {"calories": 450, "protein": 35, "carbs": 15, "fat": 25}, "availability": "always", "preparationTime": 25, "spicyLevel": 0, "vegetarian": false, "vegan": false, "glutenFree": true, "popular": true, "image": "https://example.com/salmon.jpg"}, {"id": "menu-003", "restaurantId": "rest-003", "category": "Noodles", "name": "Pad Thai", "description": "Traditional Thai stir-fried noodles with shrimp, tofu, and peanuts", "price": 13.99, "ingredients": ["rice noodles", "shrimp", "tofu", "peanuts", "bean sprouts"], "allergens": ["shellfish", "peanuts", "soy"], "nutritionalInfo": {"calories": 580, "protein": 22, "carbs": 65, "fat": 28}, "availability": "always", "preparationTime": 20, "spicyLevel": 2, "vegetarian": false, "vegan": false, "glutenFree": true, "popular": true, "image": "https://example.com/padthai.jpg"}], "testOrders": [{"id": "order-001", "restaurantId": "rest-001", "customerId": "cust-001", "orderNumber": "TB-2024-0115-001", "type": "dine_in", "status": "completed", "items": [{"menuItemId": "menu-001", "quantity": 1, "price": 12.99, "specialInstructions": ""}, {"menuItemId": "menu-002", "quantity": 2, "price": 49.98, "specialInstructions": "Medium rare"}], "subtotal": 62.97, "tax": 5.67, "tip": 12.0, "total": 80.64, "paymentMethod": "credit_card", "paymentStatus": "paid", "tableNumber": "12", "serverName": "<PERSON>", "orderTime": "2024-01-15T12:30:00Z", "completionTime": "2024-01-15T13:15:00Z", "rating": 5, "feedback": "Excellent food and service!"}, {"id": "order-002", "restaurantId": "rest-001", "customerId": "cust-002", "orderNumber": "TB-2024-0115-002", "type": "delivery", "status": "delivered", "items": [{"menuItemId": "menu-002", "quantity": 1, "price": 24.99, "specialInstructions": "No lemon please"}], "subtotal": 24.99, "tax": 2.25, "deliveryFee": 4.99, "tip": 5.0, "total": 37.23, "paymentMethod": "app_payment", "paymentStatus": "paid", "deliveryAddress": {"street": "789 Oak Street", "city": "Los Angeles", "state": "CA", "zipCode": "90015"}, "driverName": "<PERSON>", "orderTime": "2024-01-15T18:45:00Z", "deliveryTime": "2024-01-15T19:30:00Z", "rating": 4, "feedback": "Food was good but took longer than expected"}], "testTransactions": [{"id": "trans-001", "restaurantId": "rest-001", "date": "2024-01-15", "type": "sale", "amount": 2543.67, "orderCount": 45, "averageOrderValue": 56.53, "paymentBreakdown": {"cash": 543.2, "credit_card": 1678.9, "app_payment": 321.57}, "hourlyBreakdown": {"11": {"revenue": 156.3, "orders": 3}, "12": {"revenue": 487.9, "orders": 12}, "13": {"revenue": 367.2, "orders": 8}, "18": {"revenue": 567.8, "orders": 10}, "19": {"revenue": 689.3, "orders": 9}, "20": {"revenue": 275.17, "orders": 3}}}], "testReviews": [{"id": "review-001", "restaurantId": "rest-001", "customerId": "cust-001", "orderId": "order-001", "platform": "internal", "rating": 5, "title": "Amazing Experience!", "comment": "The food was absolutely delicious and the service was impeccable. Will definitely come back!", "categories": {"food": 5, "service": 5, "ambiance": 5, "value": 4}, "photos": ["https://example.com/review1-1.jpg"], "helpfulCount": 23, "responseFromOwner": "Thank you so much for your kind words! We're thrilled you enjoyed your experience.", "createdAt": "2024-01-15T15:30:00Z", "verified": true}, {"id": "review-002", "restaurantId": "rest-001", "platform": "google", "rating": 4, "title": "Good but room for improvement", "comment": "Food quality was great but service was a bit slow during peak hours.", "categories": {"food": 5, "service": 3, "ambiance": 4, "value": 4}, "photos": [], "helpfulCount": 8, "responseFromOwner": null, "createdAt": "2024-01-14T20:15:00Z", "verified": true}], "testCampaigns": [{"id": "camp-001", "restaurantId": "rest-001", "name": "Weekend Special 20% Off", "type": "discount", "status": "active", "startDate": "2024-01-13", "endDate": "2024-01-31", "budget": 1000, "spent": 234.5, "targetAudience": {"segments": ["frequent_diners", "high_value_customers"], "minOrderHistory": 3, "lastVisitDays": 30}, "channels": ["email", "sms", "in_app"], "content": {"headline": "Exclusive Weekend Special!", "body": "Enjoy 20% off your entire bill this weekend", "terms": "Valid Friday-Sunday, dine-in only, cannot be combined with other offers"}, "metrics": {"sent": 1250, "opened": 456, "clicked": 123, "redeemed": 45, "revenue": 3456.78}, "roi": 2.95}], "testInventory": [{"id": "inv-001", "restaurantId": "rest-001", "itemName": "Fresh Salmon", "category": "Seafood", "unit": "lb", "currentStock": 45.5, "reorderLevel": 20, "reorderQuantity": 50, "supplier": "Ocean Fresh Seafood", "costPerUnit": 12.5, "lastRestocked": "2024-01-14", "expirationDate": "2024-01-18", "location": "Walk-in Cooler A"}, {"id": "inv-002", "restaurantId": "rest-001", "itemName": "Olive Oil", "category": "Oils & Condiments", "unit": "gallon", "currentStock": 12, "reorderLevel": 5, "reorderQuantity": 20, "supplier": "Restaurant Supply Co", "costPerUnit": 28.0, "lastRestocked": "2024-01-10", "expirationDate": "2025-01-10", "location": "Dry Storage B"}], "testStaff": [{"id": "staff-001", "restaurantId": "rest-001", "employeeId": "EMP-1001", "name": "<PERSON>", "role": "server", "email": "<EMAIL>", "phone": "******-111-2222", "hourlyRate": 15.5, "startDate": "2023-03-15", "status": "active", "schedule": {"monday": {"start": "11:00", "end": "19:00"}, "tuesday": {"start": "11:00", "end": "19:00"}, "wednesday": "off", "thursday": {"start": "11:00", "end": "19:00"}, "friday": {"start": "11:00", "end": "19:00"}, "saturday": {"start": "10:00", "end": "18:00"}, "sunday": "off"}, "performance": {"averageRating": 4.8, "totalSales": 125678.9, "tablesServed": 1234}}, {"id": "staff-002", "restaurantId": "rest-001", "employeeId": "EMP-1002", "name": "<PERSON>", "role": "chef", "email": "<EMAIL>", "phone": "******-333-4444", "hourlyRate": 25.0, "startDate": "2022-11-01", "status": "active", "schedule": {"monday": {"start": "09:00", "end": "17:00"}, "tuesday": {"start": "09:00", "end": "17:00"}, "wednesday": {"start": "09:00", "end": "17:00"}, "thursday": {"start": "09:00", "end": "17:00"}, "friday": {"start": "09:00", "end": "17:00"}, "saturday": "off", "sunday": "off"}, "certifications": ["ServSafe", "Culinary Arts Degree"], "specialties": ["Italian", "Mediterranean", "Seafood"]}], "testNotifications": [{"id": "notif-001", "userId": "user-001", "type": "low_inventory", "priority": "high", "title": "Low Inventory Alert", "message": "Fresh Salmon is below reorder level (15.5 lb remaining)", "data": {"itemId": "inv-001", "currentLevel": 15.5, "reorderLevel": 20}, "read": false, "createdAt": "2024-01-15T09:00:00Z"}, {"id": "notif-002", "userId": "user-001", "type": "new_review", "priority": "medium", "title": "New 5-Star Review", "message": "You received a new 5-star review from a customer", "data": {"reviewId": "review-001", "rating": 5, "platform": "internal"}, "read": true, "createdAt": "2024-01-15T15:35:00Z"}], "testInsights": [{"id": "insight-001", "restaurantId": "rest-001", "type": "revenue_anomaly", "severity": "high", "title": "Unusual Revenue Spike Detected", "description": "Revenue for lunch service increased by 45% compared to the same day last week", "metrics": {"currentValue": 1234.56, "previousValue": 851.23, "change": 45.1, "period": "lunch_service"}, "recommendations": ["Analyze what drove the increase (special event, weather, promotion)", "Consider staffing adjustments for similar future patterns", "Review inventory to ensure adequate stock"], "createdAt": "2024-01-15T14:30:00Z", "acknowledged": false}, {"id": "insight-002", "restaurantId": "rest-001", "type": "menu_optimization", "severity": "medium", "title": "Menu Item Performance Opportunity", "description": "Grilled Salmon has high profit margin but low order frequency", "metrics": {"profitMargin": 68.5, "orderFrequency": 0.12, "competitorPrice": 27.99, "yourPrice": 24.99}, "recommendations": ["Consider featuring in daily specials to increase visibility", "Add appealing photos to menu and online platforms", "Train staff to recommend during appropriate occasions"], "createdAt": "2024-01-14T10:00:00Z", "acknowledged": true}], "testApiKeys": [{"id": "api-001", "userId": "user-003", "name": "Production API Key", "key": "pk_live_abcdef123456789", "permissions": ["read:analytics", "read:orders", "write:menu"], "rateLimit": 1000, "status": "active", "lastUsed": "2024-01-15T17:45:00Z", "createdAt": "2023-06-01T10:00:00Z", "expiresAt": null}, {"id": "api-002", "userId": "user-003", "name": "Development API Key", "key": "pk_test_xyz987654321", "permissions": ["read:all", "write:all"], "rateLimit": 100, "status": "active", "lastUsed": "2024-01-14T22:30:00Z", "createdAt": "2023-12-15T14:20:00Z", "expiresAt": "2024-12-15T14:20:00Z"}], "testWebhooks": [{"id": "webhook-001", "userId": "user-003", "url": "https://api.example.com/webhooks/bitebase", "events": ["order.created", "order.updated", "review.created"], "secret": "whsec_abcdef123456", "status": "active", "lastTriggered": "2024-01-15T18:50:00Z", "successRate": 98.5, "createdAt": "2023-08-20T09:00:00Z"}], "errorScenarios": {"authentication": {"invalidCredentials": {"email": "<EMAIL>", "password": "wrongpassword"}, "lockedAccount": {"email": "<EMAIL>", "password": "any_password"}, "expiredSession": {"token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.expired"}}, "validation": {"invalidEmail": ["notanemail", "@invalid.com", "user@", "<EMAIL>"], "weakPasswords": ["12345", "password", "qwerty", "abc123"], "invalidPhoneNumbers": ["123", "555-GET-FOOD", "******-555-555"], "invalidDates": ["2024-13-01", "2024-02-30", "not-a-date"], "negativeValues": [-1, -99.99, -1000], "oversizedInputs": {"name": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "description": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.", "email": "<EMAIL>"}}, "edgeCases": {"emptyRestaurant": {"id": "rest-empty", "name": "Empty Restaurant", "menuItems": [], "orders": [], "reviews": []}, "maxCapacityData": {"orders": 10000, "menuItems": 500, "staff": 200, "tables": 100}, "specialCharacters": {"restaurantName": "Café José's <Pizza> & Grill", "menuItem": "Fish & Chips (50% off!)", "customerName": "<PERSON><PERSON><PERSON>-<PERSON>, Jr."}, "unicodeData": {"restaurantName": "🍕 Émoji Café 🍔", "menuItem": "Crème brûlée à la française", "address": "東京都渋谷区 1-2-3"}}, "networkErrors": {"timeout": {"delay": 30000, "message": "Request timeout"}, "connectionRefused": {"code": "ECONNREFUSED", "message": "Connection refused"}, "dnsFailure": {"code": "ENOTFOUND", "message": "DNS lookup failed"}}, "apiErrors": {"rateLimit": {"status": 429, "message": "Rate limit exceeded", "retryAfter": 60}, "serverError": {"status": 500, "message": "Internal server error"}, "maintenance": {"status": 503, "message": "Service temporarily unavailable"}}}}