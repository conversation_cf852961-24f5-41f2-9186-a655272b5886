# BiteBase API Testing Collection

## Overview
This document provides a comprehensive API testing collection for all BiteBase backend endpoints. Each test includes request details, expected responses, and validation criteria.

## Base Configuration

### Environment Variables
```
BASE_URL: https://api.bitebase.com/v1
AUTH_TOKEN: Bearer {{access_token}}
RESTAURANT_ID: {{restaurant_id}}
USER_ID: {{user_id}}
CONTENT_TYPE: application/json
```

### Authentication Headers
```json
{
  "Authorization": "{{AUTH_TOKEN}}",
  "Content-Type": "{{CONTENT_TYPE}}",
  "X-API-Version": "1.0",
  "X-Request-ID": "{{$guid}}"
}
```

---

## 1. Authentication API Tests

### 1.1 User Registration
**POST** `/auth/register`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "firstName": "Test",
  "lastName": "User",
  "restaurantName": "Test Restaurant",
  "phone": "******-0123"
}
```

**Expected Response (201):**
```json
{
  "success": true,
  "message": "Registration successful",
  "data": {
    "userId": "uuid",
    "email": "<EMAIL>",
    "verificationRequired": true
  }
}
```

**Validation Tests:**
- Invalid email format (400)
- Weak password (400)
- Duplicate email (409)
- Missing required fields (400)
- SQL injection in fields (400)

### 1.2 User Login
**POST** `/auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "rememberMe": true
}
```

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "accessToken": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresIn": 3600,
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "restaurant_owner"
    }
  }
}
```

**Error Cases:**
- Invalid credentials (401)
- Account locked (423)
- Email not verified (403)
- Rate limit exceeded (429)

### 1.3 Refresh Token
**POST** `/auth/refresh`

**Request Body:**
```json
{
  "refreshToken": "{{refresh_token}}"
}
```

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "accessToken": "new_jwt_token",
    "expiresIn": 3600
  }
}
```

### 1.4 Password Reset Request
**POST** `/auth/forgot-password`

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Expected Response (200):**
```json
{
  "success": true,
  "message": "Password reset email sent"
}
```

### 1.5 Password Reset Confirm
**POST** `/auth/reset-password`

**Request Body:**
```json
{
  "token": "reset_token",
  "newPassword": "NewSecure123!",
  "confirmPassword": "NewSecure123!"
}
```

---

## 2. Restaurant Management API Tests

### 2.1 Get Restaurants
**GET** `/restaurants?page=1&limit=10&search=test`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "restaurants": [
      {
        "id": "uuid",
        "name": "Test Restaurant",
        "cuisine": ["Italian", "Pizza"],
        "rating": 4.5,
        "address": {
          "street": "123 Main St",
          "city": "Los Angeles",
          "state": "CA",
          "zipCode": "90001"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "pages": 5
    }
  }
}
```

### 2.2 Create Restaurant
**POST** `/restaurants`

**Request Body:**
```json
{
  "name": "New Test Restaurant",
  "cuisine": ["American", "Burgers"],
  "address": {
    "street": "456 Oak Ave",
    "city": "New York",
    "state": "NY",
    "zipCode": "10001",
    "latitude": 40.7128,
    "longitude": -74.0060
  },
  "contact": {
    "phone": "******-0456",
    "email": "<EMAIL>",
    "website": "https://newrestaurant.com"
  },
  "operatingHours": {
    "monday": {"open": "11:00", "close": "22:00"},
    "tuesday": {"open": "11:00", "close": "22:00"}
  }
}
```

### 2.3 Update Restaurant
**PUT** `/restaurants/{{restaurant_id}}`

**Request Body:**
```json
{
  "name": "Updated Restaurant Name",
  "features": ["delivery", "takeout", "dine_in"]
}
```

### 2.4 Get Restaurant Details
**GET** `/restaurants/{{restaurant_id}}`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Test Restaurant",
    "metrics": {
      "totalOrders": 1523,
      "averageRating": 4.5,
      "monthlyRevenue": 125000
    }
  }
}
```

---

## 3. Menu Management API Tests

### 3.1 Get Menu Items
**GET** `/restaurants/{{restaurant_id}}/menu?category=appetizers`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "menu-001",
        "name": "Crispy Calamari",
        "description": "Fresh calamari rings",
        "price": 12.99,
        "category": "Appetizers",
        "available": true,
        "image": "https://cdn.example.com/calamari.jpg"
      }
    ]
  }
}
```

### 3.2 Create Menu Item
**POST** `/restaurants/{{restaurant_id}}/menu`

**Request Body:**
```json
{
  "name": "New Dish",
  "description": "Delicious new creation",
  "price": 18.99,
  "category": "Main Courses",
  "ingredients": ["chicken", "vegetables", "sauce"],
  "allergens": ["gluten", "dairy"],
  "nutritionalInfo": {
    "calories": 450,
    "protein": 35,
    "carbs": 30,
    "fat": 15
  }
}
```

### 3.3 Update Menu Item
**PUT** `/restaurants/{{restaurant_id}}/menu/{{item_id}}`

**Request Body:**
```json
{
  "price": 19.99,
  "available": false,
  "reason": "Out of ingredients"
}
```

### 3.4 Menu Analysis
**GET** `/menu/analysis?restaurant_id={{restaurant_id}}&period=30days`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "topPerformers": [
      {
        "itemId": "menu-001",
        "name": "Crispy Calamari",
        "salesCount": 234,
        "revenue": 3037.66,
        "profitMargin": 65.2
      }
    ],
    "recommendations": [
      "Consider promoting 'Grilled Salmon' - high margin but low sales",
      "Remove 'Vegetable Soup' - low sales and low margin"
    ]
  }
}
```

---

## 4. Analytics API Tests

### 4.1 Dashboard Analytics
**GET** `/analytics/dashboard?restaurant_id={{restaurant_id}}&period=7days`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "revenue": {
      "total": 25340.50,
      "change": 12.5,
      "trend": "up"
    },
    "orders": {
      "total": 456,
      "average": 55.53,
      "peakHour": "19:00"
    },
    "customers": {
      "new": 89,
      "returning": 367,
      "satisfaction": 4.6
    }
  }
}
```

### 4.2 Performance Metrics
**GET** `/analytics/performance?restaurant_id={{restaurant_id}}&metric=revenue&groupBy=hour`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "metrics": [
      {"hour": "11:00", "value": 1234.50, "orders": 15},
      {"hour": "12:00", "value": 3456.78, "orders": 42},
      {"hour": "13:00", "value": 2890.12, "orders": 35}
    ],
    "summary": {
      "peak": "12:00",
      "average": 2527.13
    }
  }
}
```

### 4.3 Predictive Analytics
**GET** `/analytics/predictions?restaurant_id={{restaurant_id}}&type=revenue&days=30`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "predictions": [
      {"date": "2024-02-01", "predicted": 3456.78, "confidence": 0.85},
      {"date": "2024-02-02", "predicted": 3890.12, "confidence": 0.82}
    ],
    "factors": [
      "Historical patterns",
      "Seasonal trends",
      "Local events"
    ]
  }
}
```

---

## 5. AI & Intelligence API Tests

### 5.1 Market Analysis
**POST** `/ai/market-analysis`

**Request Body:**
```json
{
  "restaurantId": "{{restaurant_id}}",
  "radius": 5,
  "includeCompetitors": true,
  "analysisDepth": "comprehensive"
}
```

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "marketSize": 2500000,
    "competitors": 15,
    "marketShare": 12.5,
    "opportunities": [
      "Underserved breakfast market",
      "Growing demand for vegan options"
    ],
    "threats": [
      "New competitor opening nearby",
      "Rising food costs"
    ]
  }
}
```

### 5.2 Natural Language Query
**POST** `/nl-query/process`

**Request Body:**
```json
{
  "query": "What was my best selling item last month?",
  "context": {
    "restaurantId": "{{restaurant_id}}",
    "userId": "{{user_id}}"
  }
}
```

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "interpretation": "Top selling menu item for January 2024",
    "result": {
      "item": "Crispy Calamari",
      "quantity": 234,
      "revenue": 3037.66
    },
    "visualization": {
      "type": "bar_chart",
      "data": [...]
    },
    "suggestions": [
      "How does this compare to last year?",
      "What's the profit margin on this item?"
    ]
  }
}
```

### 5.3 Menu Optimization AI
**POST** `/ai/menu-optimization`

**Request Body:**
```json
{
  "restaurantId": "{{restaurant_id}}",
  "optimizationGoal": "profit_maximization",
  "constraints": {
    "maintainVariety": true,
    "priceRange": {"min": 8, "max": 35}
  }
}
```

---

## 6. Real-time WebSocket Tests

### 6.1 Connect to Analytics Stream
**WebSocket** `wss://api.bitebase.com/v1/realtime/analytics/ws/{{user_id}}`

**Connection Message:**
```json
{
  "type": "subscribe",
  "channels": ["orders", "analytics", "alerts"],
  "restaurantIds": ["{{restaurant_id}}"]
}
```

**Expected Messages:**
```json
{
  "type": "order_update",
  "data": {
    "orderId": "order-123",
    "status": "preparing",
    "estimatedTime": 15
  }
}
```

### 6.2 Collaboration WebSocket
**WebSocket** `wss://api.bitebase.com/v1/collaboration/ws/{{dashboard_id}}/{{user_id}}`

**Presence Update:**
```json
{
  "type": "presence",
  "users": [
    {"userId": "user-1", "name": "John", "cursor": {"x": 100, "y": 200}}
  ]
}
```

---

## 7. Integration API Tests

### 7.1 POS Integration Setup
**POST** `/pos-integration/integrations`

**Request Body:**
```json
{
  "restaurantId": "{{restaurant_id}}",
  "provider": "square",
  "credentials": {
    "accessToken": "{{encrypted_token}}",
    "locationId": "{{location_id}}"
  },
  "syncSettings": {
    "autoSync": true,
    "syncInterval": 300,
    "syncItems": ["orders", "payments", "inventory"]
  }
}
```

### 7.2 Test POS Connection
**POST** `/pos-integration/integrations/{{integration_id}}/test`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "connected": true,
    "lastSync": "2024-01-15T10:30:00Z",
    "itemsFound": {
      "menuItems": 45,
      "categories": 8
    }
  }
}
```

### 7.3 Sync POS Data
**POST** `/pos-integration/integrations/{{integration_id}}/sync`

**Request Body:**
```json
{
  "syncType": "full",
  "includHistorical": true,
  "dateRange": {
    "start": "2024-01-01",
    "end": "2024-01-15"
  }
}
```

---

## 8. Campaign Management API Tests

### 8.1 Create Campaign
**POST** `/campaign-management/campaigns`

**Request Body:**
```json
{
  "name": "Weekend Special",
  "type": "discount",
  "target": {
    "segments": ["frequent_diners"],
    "minVisits": 3
  },
  "offer": {
    "type": "percentage",
    "value": 20,
    "conditions": {
      "minOrder": 50,
      "validDays": ["friday", "saturday", "sunday"]
    }
  },
  "schedule": {
    "startDate": "2024-02-01",
    "endDate": "2024-02-29"
  },
  "channels": ["email", "sms", "app"]
}
```

### 8.2 Get Campaign Performance
**GET** `/campaign-management/campaigns/{{campaign_id}}/performance`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "metrics": {
      "sent": 1500,
      "opened": 750,
      "clicked": 225,
      "redeemed": 87,
      "revenue": 7845.30
    },
    "roi": 3.2,
    "conversionRate": 5.8
  }
}
```

---

## 9. Security API Tests

### 9.1 RBAC Permission Check
**POST** `/security/rbac/check-access`

**Request Body:**
```json
{
  "userId": "{{user_id}}",
  "resource": "financial_reports",
  "action": "view",
  "context": {
    "restaurantId": "{{restaurant_id}}",
    "reportType": "profit_loss"
  }
}
```

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "allowed": true,
    "role": "restaurant_owner",
    "permissions": ["view_all_reports", "export_data"]
  }
}
```

### 9.2 Audit Log Query
**GET** `/security/audit/events?userId={{user_id}}&action=data_export&days=7`

**Expected Response (200):**
```json
{
  "success": true,
  "data": {
    "events": [
      {
        "id": "audit-001",
        "timestamp": "2024-01-15T14:30:00Z",
        "userId": "user-123",
        "action": "data_export",
        "resource": "financial_report",
        "ipAddress": "***********",
        "result": "success"
      }
    ]
  }
}
```

---

## 10. Payment & Subscription API Tests

### 10.1 Create Payment Intent
**POST** `/payments/payment-intent`

**Request Body:**
```json
{
  "amount": 9900,
  "currency": "usd",
  "paymentMethod": "card",
  "metadata": {
    "userId": "{{user_id}}",
    "plan": "professional",
    "period": "monthly"
  }
}
```

### 10.2 Update Subscription
**PUT** `/payments/subscriptions/{{subscription_id}}`

**Request Body:**
```json
{
  "plan": "enterprise",
  "paymentMethod": "pm_123456",
  "prorationBehavior": "create_prorations"
}
```

---

## Error Response Format

All error responses follow this format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "requestId": "req_123456"
}
```

## Common Error Codes

- `AUTHENTICATION_REQUIRED` (401)
- `INSUFFICIENT_PERMISSIONS` (403)
- `RESOURCE_NOT_FOUND` (404)
- `VALIDATION_ERROR` (400)
- `RATE_LIMIT_EXCEEDED` (429)
- `INTERNAL_SERVER_ERROR` (500)
- `SERVICE_UNAVAILABLE` (503)

---

## Rate Limiting

All API endpoints are rate limited:

- **Standard endpoints:** 100 requests/minute
- **Analytics endpoints:** 50 requests/minute
- **AI endpoints:** 20 requests/minute
- **Bulk operations:** 10 requests/minute

Rate limit headers:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1615847400
```

---

## Testing Best Practices

### 1. Authentication Testing
- Always test with expired tokens
- Test with invalid tokens
- Test with tokens from different users
- Verify proper scope enforcement

### 2. Input Validation
- Test with missing required fields
- Test with invalid data types
- Test with boundary values
- Test with special characters
- Test with SQL injection attempts
- Test with oversized payloads

### 3. Performance Testing
- Measure response times
- Test with large datasets
- Test concurrent requests
- Monitor rate limiting
- Check pagination efficiency

### 4. Error Handling
- Verify error message clarity
- Check error code consistency
- Test recovery procedures
- Validate rollback on failures

### 5. Security Testing
- Test unauthorized access
- Verify data isolation
- Check encryption in transit
- Test CORS policies
- Validate input sanitization

---

## Postman Collection Structure

```
BiteBase API Tests/
├── Authentication/
│   ├── Register
│   ├── Login
│   ├── Refresh Token
│   └── Password Reset
├── Restaurants/
│   ├── CRUD Operations
│   ├── Menu Management
│   └── Staff Management
├── Analytics/
│   ├── Dashboard
│   ├── Reports
│   └── Predictions
├── AI Features/
│   ├── Market Analysis
│   ├── Natural Language
│   └── Recommendations
├── Integrations/
│   ├── POS Systems
│   ├── Delivery Platforms
│   └── Payment Providers
├── Real-time/
│   ├── WebSocket Tests
│   └── SSE Tests
└── Security/
    ├── RBAC Tests
    ├── Audit Logs
    └── Vulnerability Tests
```

---

## Automation Scripts

### Pre-request Script (Authentication)
```javascript
// Get or refresh token
if (!pm.globals.get("access_token") || pm.globals.get("token_expiry") < Date.now()) {
    pm.sendRequest({
        url: pm.environment.get("BASE_URL") + "/auth/login",
        method: "POST",
        header: {"Content-Type": "application/json"},
        body: {
            mode: "raw",
            raw: JSON.stringify({
                email: pm.environment.get("TEST_EMAIL"),
                password: pm.environment.get("TEST_PASSWORD")
            })
        }
    }, function(err, res) {
        if (!err && res.code === 200) {
            const response = res.json();
            pm.globals.set("access_token", response.data.accessToken);
            pm.globals.set("token_expiry", Date.now() + (response.data.expiresIn * 1000));
        }
    });
}
```

### Test Script (Response Validation)
```javascript
// Common response validation
pm.test("Status code is 200", function() {
    pm.response.to.have.status(200);
});

pm.test("Response time is less than 500ms", function() {
    pm.expect(pm.response.responseTime).to.be.below(500);
});

pm.test("Response has required fields", function() {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property("success");
    pm.expect(jsonData).to.have.property("data");
});

pm.test("No security headers exposed", function() {
    pm.expect(pm.response.headers.get("X-Powered-By")).to.be.null;
});
```

---

## Conclusion

This API testing collection provides comprehensive coverage of all BiteBase endpoints. Regular execution ensures API reliability, performance, and security. Automate these tests in CI/CD pipeline for continuous validation.