# Technical Implementation Plan: CopilotKit Integration

## Architecture Overview

### System Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │   MCP Server     │    │   Backend API   │
│   (Next.js)     │◄──►│   (@genkit-ai)   │◄──►│   (FastAPI)     │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ CopilotKit  │ │    │ │ Restaurant   │ │    │ │ Business    │ │
│ │ Components  │ │    │ │ Intelligence │ │    │ │ Logic       │ │
│ │             │ │    │ │ Tools        │ │    │ │             │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Technology Stack Integration
- **Frontend**: Next.js 15.4.4 + CopilotKit React components
- **MCP Server**: @genkit-ai/mcp (already installed)
- **AI Models**: OpenAI GPT-4 / Anthropic Claude via CopilotKit
- **Backend**: Existing FastAPI with enhanced tool endpoints
- **Database**: Existing SQLite/PostgreSQL for data persistence

## Phase 1: Foundation Setup (Week 1-2)

### 1.1 CopilotKit Installation & Configuration

#### Package Installation
```bash
# Frontend dependencies
cd frontend
npm install @copilotkit/react-core @copilotkit/react-ui @copilotkit/react-textarea

# Backend MCP server enhancement
cd ../
npm install @copilotkit/backend @copilotkit/shared
```

#### Environment Configuration
```typescript
// frontend/.env.local
NEXT_PUBLIC_COPILOT_CLOUD_API_KEY=your_api_key
COPILOT_CLOUD_API_KEY=your_api_key
MCP_SERVER_URL=http://localhost:8001/mcp
```

#### Provider Setup
```typescript
// frontend/src/app/layout.tsx
import { CopilotKit } from "@copilotkit/react-core";

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <CopilotKit 
          runtimeUrl="/api/copilotkit"
          agent="restaurant_intelligence_agent"
        >
          {children}
        </CopilotKit>
      </body>
    </html>
  );
}
```

### 1.2 MCP Server Enhancement

#### Server Configuration
```typescript
// mcp-server/restaurant-intelligence-server.ts
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';

const server = new Server(
  {
    name: 'restaurant-intelligence-server',
    version: '1.0.0',
  },
  {
    capabilities: {
      tools: {},
      resources: {},
    },
  }
);

// Tool definitions
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'analyze_location',
        description: 'Analyze restaurant location potential and market data',
        inputSchema: {
          type: 'object',
          properties: {
            latitude: { type: 'number' },
            longitude: { type: 'number' },
            radius_km: { type: 'number', default: 2 }
          },
          required: ['latitude', 'longitude']
        }
      },
      // Additional tools...
    ]
  };
});
```

#### Tool Implementation
```typescript
// Tool handlers
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;
  
  switch (name) {
    case 'analyze_location':
      return await analyzeLocation(args);
    case 'generate_market_report':
      return await generateMarketReport(args);
    case 'get_competitors':
      return await getCompetitors(args);
    default:
      throw new Error(`Unknown tool: ${name}`);
  }
});
```

### 1.3 API Route Setup
```typescript
// frontend/src/app/api/copilotkit/route.ts
import { CopilotRuntime, OpenAIAdapter } from "@copilotkit/backend";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  const { handleRequest } = CopilotRuntime({
    serviceAdapter: new OpenAIAdapter(),
    actions: [
      {
        name: "analyzeLocation",
        description: "Analyze restaurant location potential",
        parameters: {
          type: "object",
          properties: {
            latitude: { type: "number" },
            longitude: { type: "number" },
            radius: { type: "number" }
          }
        },
        handler: async ({ latitude, longitude, radius }) => {
          const response = await fetch(`${process.env.BACKEND_URL}/api/locations/analyze`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ latitude, longitude, radius_km: radius })
          });
          return await response.json();
        }
      }
    ]
  });

  return handleRequest(req);
}
```

## Phase 2: Component Enhancement (Week 3-5)

### 2.1 Enhanced Floating Chatbot Replacement

#### New Component Structure
```typescript
// frontend/src/components/ai/CopilotKitChatbot.tsx
import { CopilotPopup, useCopilotAction, useCopilotChat } from "@copilotkit/react-core";

export function CopilotKitChatbot() {
  // Location analysis tool
  useCopilotAction({
    name: "analyzeLocation",
    description: "Analyze restaurant location potential and market data",
    parameters: [
      { name: "latitude", type: "number", description: "Latitude coordinate" },
      { name: "longitude", type: "number", description: "Longitude coordinate" },
      { name: "radius", type: "number", description: "Analysis radius in kilometers" }
    ],
    handler: async ({ latitude, longitude, radius = 2 }) => {
      const analysis = await apiClient.locations.analyze({
        latitude,
        longitude,
        radius_km: radius
      });
      return {
        success: true,
        data: analysis,
        summary: `Analyzed location at ${latitude}, ${longitude} within ${radius}km radius`
      };
    }
  });

  // Market report generation tool
  useCopilotAction({
    name: "generateMarketReport",
    description: "Generate comprehensive market analysis report",
    parameters: [
      { name: "location", type: "object", description: "Location coordinates" },
      { name: "reportType", type: "string", description: "Type of report to generate" }
    ],
    renderAndWaitForResponse: ({ args, respond }) => (
      <MarketReportApproval
        location={args.location}
        reportType={args.reportType}
        onApprove={(config) => respond({ approved: true, config })}
        onReject={() => respond({ approved: false })}
      />
    ),
    handler: async ({ location, reportType, approved, config }) => {
      if (!approved) return { success: false, message: "Report generation cancelled" };
      
      const report = await apiClient.reports.generate({
        location,
        type: reportType,
        config
      });
      return { success: true, report };
    }
  });

  return (
    <CopilotPopup
      instructions="You are a restaurant intelligence assistant. Help users analyze locations, generate reports, and understand market data. Use the available tools to provide accurate, data-driven insights."
      labels={{
        title: "BiteBase AI Assistant",
        initial: "How can I help you with restaurant intelligence today?"
      }}
      defaultOpen={false}
    />
  );
}
```

### 2.2 Market Report Agent Enhancement

#### Conversational Interface
```typescript
// frontend/src/components/interactive/ConversationalMarketAgent.tsx
import { CopilotChat, useCopilotAction } from "@copilotkit/react-core";

export function ConversationalMarketAgent() {
  const [reportData, setReportData] = useState(null);

  useCopilotAction({
    name: "searchCompetitors",
    description: "Search for competitors in a specific area",
    parameters: [
      { name: "location", type: "string", description: "Location to search" },
      { name: "cuisine", type: "string", description: "Cuisine type filter" },
      { name: "radius", type: "number", description: "Search radius in km" }
    ],
    handler: async ({ location, cuisine, radius }) => {
      const competitors = await apiClient.competitors.search({
        location,
        cuisine,
        radius_km: radius
      });
      return competitors;
    }
  });

  useCopilotAction({
    name: "analyzeDemographics",
    description: "Analyze demographic data for a location",
    parameters: [
      { name: "latitude", type: "number" },
      { name: "longitude", type: "number" }
    ],
    render: ({ args, status }) => (
      <DemographicAnalysisPreview
        location={args}
        isLoading={status === "executing"}
      />
    ),
    handler: async ({ latitude, longitude }) => {
      const demographics = await apiClient.demographics.analyze({
        latitude,
        longitude
      });
      return demographics;
    }
  });

  return (
    <div className="space-y-4">
      <CopilotChat
        instructions="You are a market research specialist. Help users understand their market, analyze competitors, and identify opportunities. Use the available tools to provide comprehensive market insights."
        className="h-96"
      />
      
      {reportData && (
        <MarketReportDisplay data={reportData} />
      )}
    </div>
  );
}
```

## Phase 3: Advanced Features (Week 6-8)

### 3.1 Agentic Generative UI Implementation

#### Dynamic Map Overlays
```typescript
// frontend/src/components/interactive/AgenticInteractiveMap.tsx
import { useCoAgentStateRender } from "@copilotkit/react-core";

export function AgenticInteractiveMap() {
  const [mapState, setMapState] = useState({
    center: [13.7563, 100.5018],
    zoom: 12,
    analysisType: null
  });

  // Dynamic overlay rendering based on agent state
  useCoAgentStateRender({
    name: "map_analyzer",
    render: ({ state }) => {
      switch (state.analysis_type) {
        case 'demographic':
          return (
            <DemographicOverlay
              data={state.demographic_data}
              bounds={state.analysis_bounds}
            />
          );
        case 'competitive':
          return (
            <CompetitorOverlay
              competitors={state.competitors}
              heatmap={state.competition_heatmap}
            />
          );
        case 'opportunity':
          return (
            <OpportunityOverlay
              opportunities={state.opportunities}
              scoring={state.opportunity_scores}
            />
          );
        default:
          return null;
      }
    }
  });

  useCopilotAction({
    name: "analyzeMapArea",
    description: "Analyze a specific area on the map",
    parameters: [
      { name: "bounds", type: "object", description: "Map bounds to analyze" },
      { name: "analysisType", type: "string", description: "Type of analysis" }
    ],
    handler: async ({ bounds, analysisType }) => {
      const analysis = await apiClient.map.analyzeArea({
        bounds,
        analysis_type: analysisType
      });
      
      // Update agent state for dynamic rendering
      return {
        analysis_type: analysisType,
        analysis_bounds: bounds,
        ...analysis
      };
    }
  });

  return (
    <div className="relative h-96">
      <MapContainer
        center={mapState.center}
        zoom={mapState.zoom}
        className="h-full w-full"
      >
        <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
        
        {/* Dynamic overlays will be rendered here by useCoAgentStateRender */}
        
        <MapClickHandler onMapClick={handleMapClick} />
      </MapContainer>
    </div>
  );
}
```

### 3.2 Smart Dashboard Widgets

#### AI-Generated Widget System
```typescript
// frontend/src/components/dashboard/SmartDashboardBuilder.tsx
import { useCopilotAction } from "@copilotkit/react-core";

export function SmartDashboardBuilder() {
  const [dashboardConfig, setDashboardConfig] = useState([]);

  useCopilotAction({
    name: "suggestDashboardLayout",
    description: "Suggest optimal dashboard layout based on user data and goals",
    parameters: [
      { name: "userRole", type: "string", description: "User's role (owner, manager, analyst)" },
      { name: "businessType", type: "string", description: "Type of restaurant business" },
      { name: "dataAvailable", type: "array", description: "Available data sources" }
    ],
    renderAndWaitForResponse: ({ args, respond }) => (
      <DashboardLayoutApproval
        suggestions={args.suggestions}
        onApprove={(layout) => respond({ approved: true, layout })}
        onCustomize={(layout) => respond({ approved: true, layout, customized: true })}
        onReject={() => respond({ approved: false })}
      />
    ),
    handler: async ({ userRole, businessType, dataAvailable, approved, layout }) => {
      if (!approved) return { success: false };
      
      setDashboardConfig(layout);
      return { success: true, layout };
    }
  });

  useCopilotAction({
    name: "generateCustomWidget",
    description: "Generate a custom widget based on specific requirements",
    parameters: [
      { name: "dataSource", type: "string", description: "Data source for the widget" },
      { name: "visualizationType", type: "string", description: "Type of visualization" },
      { name: "metrics", type: "array", description: "Metrics to display" }
    ],
    render: ({ args, status }) => (
      <WidgetPreview
        config={args}
        isGenerating={status === "executing"}
      />
    ),
    handler: async ({ dataSource, visualizationType, metrics }) => {
      const widgetConfig = await generateWidgetConfig({
        dataSource,
        visualizationType,
        metrics
      });
      return widgetConfig;
    }
  });

  return (
    <div className="space-y-6">
      <CopilotChat
        instructions="I can help you build the perfect dashboard for your restaurant business. Tell me about your role, business type, and what insights you're looking for."
        className="h-64"
      />
      
      <DashboardGrid config={dashboardConfig} />
    </div>
  );
}
```

## Phase 4: MCP Server Tools (Week 9-10)

### 4.1 Restaurant Intelligence Tools

#### Location Analysis Tools
```typescript
// mcp-server/tools/location-tools.ts
export async function analyzeLocation(args: {
  latitude: number;
  longitude: number;
  radius_km?: number;
}) {
  const { latitude, longitude, radius_km = 2 } = args;
  
  try {
    // Call backend API for location analysis
    const response = await fetch(`${BACKEND_URL}/api/locations/analyze`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ latitude, longitude, radius_km })
    });
    
    const analysis = await response.json();
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          location: { latitude, longitude },
          radius_km,
          analysis: {
            demographic_score: analysis.demographic_score,
            competition_level: analysis.competition_level,
            foot_traffic: analysis.foot_traffic,
            market_potential: analysis.market_potential,
            recommendations: analysis.recommendations
          }
        })
      }]
    };
  } catch (error) {
    throw new Error(`Location analysis failed: ${error.message}`);
  }
}

export async function getCompetitors(args: {
  latitude: number;
  longitude: number;
  radius_km?: number;
  cuisine_type?: string;
}) {
  const { latitude, longitude, radius_km = 2, cuisine_type } = args;
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/competitors/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        latitude, 
        longitude, 
        radius_km, 
        cuisine_type 
      })
    });
    
    const competitors = await response.json();
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          location: { latitude, longitude },
          search_radius: radius_km,
          cuisine_filter: cuisine_type,
          competitors: competitors.map(c => ({
            name: c.name,
            distance: c.distance,
            rating: c.rating,
            price_range: c.price_range,
            cuisine_type: c.cuisine_type,
            estimated_revenue: c.estimated_revenue
          }))
        })
      }]
    };
  } catch (error) {
    throw new Error(`Competitor search failed: ${error.message}`);
  }
}
```

### 4.2 Market Research Tools
```typescript
// mcp-server/tools/market-tools.ts
export async function generateMarketReport(args: {
  location: { latitude: number; longitude: number };
  report_type: string;
  time_frame?: string;
}) {
  const { location, report_type, time_frame = '12_months' } = args;
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/reports/generate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        location,
        report_type,
        time_frame
      })
    });
    
    const report = await response.json();
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify({
          report_id: report.id,
          location,
          type: report_type,
          generated_at: new Date().toISOString(),
          executive_summary: report.executive_summary,
          key_findings: report.key_findings,
          recommendations: report.recommendations,
          data_sources: report.data_sources
        })
      }]
    };
  } catch (error) {
    throw new Error(`Market report generation failed: ${error.message}`);
  }
}
```

## Deployment & Testing Strategy

### Development Environment
```bash
# Start all services
./start-services.sh

# Start MCP server
node mcp-server/restaurant-intelligence-server.js

# Start frontend with CopilotKit
cd frontend && npm run dev

# Start backend API
cd backend && python -m uvicorn app.main:app --reload
```

### Testing Approach
1. **Unit Tests**: Individual tool functions and components
2. **Integration Tests**: CopilotKit + MCP server + Backend API
3. **E2E Tests**: Complete user workflows with AI interactions
4. **Performance Tests**: Response times and system load
5. **User Acceptance Tests**: Real user scenarios and feedback

### Monitoring & Observability
- AI interaction logging
- Tool execution metrics
- Performance monitoring
- Error tracking and alerting
- User engagement analytics

This technical implementation plan provides a comprehensive roadmap for integrating CopilotKit's agentic UI capabilities into BiteBase Intelligence, leveraging the existing MCP server infrastructure and enhancing the user experience with intelligent, conversational interfaces.
