#!/bin/bash

# BiteBase Intelligence - One Command Startup Script
# This script sets up and starts both frontend and backend services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check if running in development mode
DEVELOPMENT_MODE=${1:-"dev"}

# Print banner
clear
echo -e "${CYAN}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  ██████╗ ██╗████████╗███████╗██████╗  █████╗ ███████╗███████╗║
║  ██╔══██╗██║╚══██╔══╝██╔════╝██╔══██╗██╔══██╗██╔════╝██╔════╝║
║  ██████╔╝██║   ██║   █████╗  ██████╔╝███████║███████╗█████╗  ║
║  ██╔══██╗██║   ██║   ██╔══╝  ██╔══██╗██╔══██║╚════██║██╔══╝  ║
║  ██████╔╝██║   ██║   ███████╗██████╔╝██║  ██║███████║███████╗║
║  ╚═════╝ ╚═╝   ╚═╝   ╚══════╝╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝║
║                                                              ║
║                    INTELLIGENCE PLATFORM                     ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

print_header "🚀 Starting BiteBase Intelligence Platform..."
print_status "Mode: $DEVELOPMENT_MODE"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
print_header "📋 Checking Prerequisites..."

if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

if ! command_exists python3; then
    print_error "Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

if ! command_exists pip; then
    print_error "pip is not installed. Please install pip first."
    exit 1
fi

print_success "All prerequisites are met!"

# Function to cleanup on exit
cleanup() {
    print_warning "Shutting down services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    print_success "Services stopped. Goodbye!"
    exit 0
}

# Trap SIGINT (Ctrl+C) and SIGTERM
trap cleanup SIGINT SIGTERM

# Setup Backend
print_header "🐍 Setting up Backend..."
cd backend

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
print_status "Installing Python dependencies..."
pip install --upgrade pip
pip install -r requirements.txt

# Run database migrations (if needed)
if [ -f "alembic.ini" ]; then
    print_status "Running database migrations..."
    alembic upgrade head || print_warning "Migration failed or no migrations to run"
fi

# Start backend server (using simplified version for development)
print_status "Starting backend server on http://localhost:8000"
if [ "$DEVELOPMENT_MODE" = "prod" ]; then
    python -m uvicorn app.main_simple:app --host 0.0.0.0 --port 8000 &
else
    python -m uvicorn app.main_simple:app --host 0.0.0.0 --port 8000 --reload &
fi
BACKEND_PID=$!

# Give backend time to start
sleep 3

# Check if backend is running
if kill -0 $BACKEND_PID 2>/dev/null; then
    print_success "Backend server started successfully (PID: $BACKEND_PID)"
else
    print_error "Failed to start backend server"
    exit 1
fi

# Setup Frontend
print_header "⚛️  Setting up Frontend..."
cd ../frontend

# Install dependencies
print_status "Installing Node.js dependencies..."
if [ ! -d "node_modules" ]; then
    print_status "Installing fresh dependencies..."
    rm -f yarn.lock  # Remove yarn.lock to avoid conflicts
    npm install --legacy-peer-deps
else
    print_status "Dependencies already installed. Checking for updates..."
    rm -f yarn.lock  # Remove yarn.lock to avoid conflicts
    npm install --legacy-peer-deps
fi

# Start frontend server
print_status "Starting frontend server on http://localhost:3000"
if [ "$DEVELOPMENT_MODE" = "prod" ]; then
    npm run build
    npm start &
else
    npm run dev &
fi
FRONTEND_PID=$!

# Give frontend time to start
sleep 5

# Check if frontend is running
if kill -0 $FRONTEND_PID 2>/dev/null; then
    print_success "Frontend server started successfully (PID: $FRONTEND_PID)"
else
    print_error "Failed to start frontend server"
    cleanup
    exit 1
fi

# Success message
print_header "✅ BiteBase Intelligence Platform is now running!"
echo ""
print_success "🌐 Frontend: http://localhost:3000"
print_success "🔧 Backend API: http://localhost:8000"
print_success "📚 API Documentation: http://localhost:8000/docs"
echo ""
print_status "Press Ctrl+C to stop all services"
echo ""

# Keep script running and monitor processes
while true; do
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        print_error "Backend process died unexpectedly"
        cleanup
        exit 1
    fi
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        print_error "Frontend process died unexpectedly"
        cleanup
        exit 1
    fi
    
    sleep 5
done
