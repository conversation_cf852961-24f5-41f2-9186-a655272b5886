import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Mock function to check if user has completed restaurant setup
// In a real app, this would check against your database/API
function hasCompletedSetup(request: NextRequest): boolean {
  // For now, check if setup cookie exists
  const setupComplete = request.cookies.get('restaurant_setup_complete');
  return setupComplete?.value === 'true';
}

// Mock function to check if user is authenticated
function isAuthenticated(request: NextRequest): boolean {
  // Check for auth token in cookies or headers
  const authToken = request.cookies.get('auth_token') || request.headers.get('authorization');
  return !!authToken;
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Allow public routes (landing page, auth, etc.)
  const publicRoutes = ['/', '/auth', '/api'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Check authentication for protected routes
  if (!isAuthenticated(request)) {
    const url = request.nextUrl.clone();
    url.pathname = '/auth';
    url.searchParams.set('redirectTo', pathname);
    return NextResponse.redirect(url);
  }

  // Setup completion check for advanced features
  const setupRequiredRoutes = [
    '/dashboard',
    '/analytics',
    '/location-intelligence',
    '/research-agent',
    '/reports',
    '/campaign-management',
    '/multi-location',
    '/pos-integration'
  ];

  const requiresSetup = setupRequiredRoutes.some(route => pathname.startsWith(route));

  if (requiresSetup && !hasCompletedSetup(request)) {
    // Redirect to workspace with setup prompt
    const url = request.nextUrl.clone();
    url.pathname = '/workspace';
    url.searchParams.set('setup', 'required');
    return NextResponse.redirect(url);
  }

  // Special handling for old dashboard route - redirect to workspace
  if (pathname === '/dashboard' && !hasCompletedSetup(request)) {
    const url = request.nextUrl.clone();
    url.pathname = '/workspace';
    return NextResponse.redirect(url);
  }

  // Add security headers
  const response = NextResponse.next();
  
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'origin-when-cross-origin');
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
  );

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};