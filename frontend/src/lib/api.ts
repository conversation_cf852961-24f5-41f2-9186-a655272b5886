/**
 * API Client for BiteBase Intelligence Frontend
 * Provides centralized API communication with the backend
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  timestamp?: string;
  error?: string;
  status_code?: number;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  company?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  company?: string;
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

class ApiClient {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
    // Load token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('accessToken');
    }
  }

  /**
   * Set authentication token
   */
  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('accessToken', token);
    }
  }

  /**
   * Get authentication token
   */
  getToken(): string | null {
    return this.token;
  }

  /**
   * Clear authentication token
   */
  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
    }
  }

  /**
   * Get authentication headers
   */
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  /**
   * Make authenticated API request
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      headers: this.getHeaders(),
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // Authentication methods
  
  /**
   * Login user
   */
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.post<AuthResponse>('/api/v1/auth/login', credentials);
    
    if (response.success && response.data?.tokens) {
      this.setToken(response.data.tokens.accessToken);
      if (typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);
      }
    }
    
    return response;
  }

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.post<AuthResponse>('/api/v1/auth/register', userData);
    
    if (response.success && response.data?.tokens) {
      this.setToken(response.data.tokens.accessToken);
      if (typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);
      }
    }
    
    return response;
  }

  /**
   * Logout user
   */
  async logout(): Promise<ApiResponse> {
    try {
      const response = await this.post('/api/v1/auth/logout');
      this.clearToken();
      return response;
    } catch (error) {
      // Clear token even if logout request fails
      this.clearToken();
      throw error;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<ApiResponse<{ tokens: AuthTokens }>> {
    const refreshToken = typeof window !== 'undefined' 
      ? localStorage.getItem('refreshToken') 
      : null;

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.post<{ tokens: AuthTokens }>('/api/v1/auth/refresh', {
      refreshToken,
    });

    if (response.success && response.data?.tokens) {
      this.setToken(response.data.tokens.accessToken);
      if (typeof window !== 'undefined') {
        localStorage.setItem('refreshToken', response.data.tokens.refreshToken);
      }
    }

    return response;
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
    return this.get<{ user: User }>('/api/v1/auth/me');
  }

  /**
   * Verify token validity
   */
  async verifyToken(): Promise<ApiResponse<{ user: User }>> {
    return this.get<{ user: User }>('/api/v1/auth/verify');
  }

  // Health check methods

  /**
   * Check backend health
   */
  async healthCheck(): Promise<ApiResponse> {
    return this.get('/health');
  }

  // Analytics methods

  /**
   * Get dashboard data
   */
  async getDashboard(): Promise<ApiResponse> {
    return this.get('/api/v1/analytics/dashboard');
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(): Promise<ApiResponse> {
    return this.get('/api/v1/analytics/performance');
  }

  // Restaurant methods

  /**
   * Get restaurants
   */
  async getRestaurants(): Promise<ApiResponse> {
    return this.get('/api/v1/restaurants');
  }

  /**
   * Create new restaurant
   */
  async createRestaurant(restaurantData: any): Promise<ApiResponse> {
    return this.post('/api/v1/restaurants', restaurantData);
  }

  /**
   * Get restaurant by ID
   */
  async getRestaurant(id: string): Promise<ApiResponse> {
    return this.get(`/api/v1/restaurants/${id}`);
  }

  // AI methods

  /**
   * Submit AI query
   */
  async aiQuery(query: string): Promise<ApiResponse> {
    return this.post('/api/v1/ai/query', { query });
  }

  /**
   * Get AI insights
   */
  async getAiInsights(): Promise<ApiResponse> {
    return this.get('/api/v1/ai/insights');
  }

  /**
   * Natural language query
   */
  async naturalLanguageQuery(question: string): Promise<ApiResponse> {
    return this.post('/api/v1/nl-query/ask', { question });
  }
}

// Create and export a default instance
export const apiClient = new ApiClient();

// Export the class for custom instances
export default ApiClient;

// Utility functions for common operations
export const auth = {
  isAuthenticated: (): boolean => {
    if (typeof window === 'undefined') return false;
    return !!localStorage.getItem('accessToken');
  },

  getToken: (): string | null => {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem('accessToken');
  },

  getUser: (): User | null => {
    if (typeof window === 'undefined') return null;
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  setUser: (user: User): void => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify(user));
    }
  },

  clearAuth: (): void => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
    }
  },
};
