/**
 * Simple language utility without React Context
 * This provides basic translation functionality without the complexity of Context API
 */

export type Language = 'en';

// Simple translation function - currently just returns the key
export function t(key: string, params?: Record<string, any>): string {
  let result = key;
  
  // Handle basic interpolation
  if (params && typeof result === 'string') {
    Object.keys(params).forEach(param => {
      result = result.replace(new RegExp(`{${param}}`, 'g'), params[param]);
    });
  }
  
  return result;
}

// Simple language functions
export function getCurrentLanguage(): Language {
  return 'en';
}

export function setCurrentLanguage(lang: Language): void {
  // No-op for now - could be extended to use localStorage
  console.log(`Language set to: ${lang}`);
}

// Export a hook-like function for compatibility
export function useLanguage() {
  return {
    language: getCurrentLanguage(),
    setLanguage: setCurrentLanguage,
    t
  };
}