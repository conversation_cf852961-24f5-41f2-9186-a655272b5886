# BiteBase Intelligence Translation Architecture

## Overview

This document outlines the comprehensive translation architecture for the BiteBase Intelligence bilingual (Thai and English) Next.js application. The architecture is designed to be scalable, maintainable, and performant while supporting the application's extensive feature set.

## File Structure

```
frontend/src/locales/
├── index.ts                    # Main export file for translation system
├── types.ts                    # TypeScript types for translations
├── utils.ts                    # Utility functions for translation management
├── en/                         # English translations
│   ├── index.ts               # Aggregates all English translations
│   ├── common.ts              # Common/shared translations
│   ├── auth.ts                # Authentication related
│   ├── dashboard.ts           # Dashboard feature
│   ├── analytics.ts           # Analytics module
│   ├── insights.ts            # AI Insights feature
│   ├── location.ts            # Location Intelligence
│   ├── restaurant.ts          # Restaurant Management
│   ├── campaign.ts            # Campaign Management
│   ├── pos.ts                 # POS Integration
│   ├── price.ts               # Price Intelligence
│   ├── product.ts             # Product Intelligence
│   ├── promotion.ts           # Promotion Intelligence
│   ├── reports.ts             # Reports module
│   ├── settings.ts            # Settings pages
│   ├── errors.ts              # Error messages
│   ├── validation.ts          # Form validation messages
│   └── components/            # Component-specific translations
│       ├── charts.ts          # Chart components
│       ├── tables.ts          # Table components
│       ├── forms.ts           # Form components
│       └── modals.ts          # Modal components
└── th/                         # Thai translations (mirrors en/ structure)
    ├── index.ts
    ├── common.ts
    ├── auth.ts
    ├── dashboard.ts
    ├── analytics.ts
    ├── insights.ts
    ├── location.ts
    ├── restaurant.ts
    ├── campaign.ts
    ├── pos.ts
    ├── price.ts
    ├── product.ts
    ├── promotion.ts
    ├── reports.ts
    ├── settings.ts
    ├── errors.ts
    ├── validation.ts
    └── components/
        ├── charts.ts
        ├── tables.ts
        ├── forms.ts
        └── modals.ts
```

## Translation Key Structure

### Hierarchical Nested Structure

We will use a hierarchical nested object structure for better organization and IntelliSense support:

```typescript
// Example: en/common.ts
export const common = {
  actions: {
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    create: 'Create',
    update: 'Update',
    submit: 'Submit',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    close: 'Close',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    export: 'Export',
    import: 'Import',
    download: 'Download',
    upload: 'Upload',
    refresh: 'Refresh',
    reset: 'Reset'
  },
  status: {
    loading: 'Loading...',
    saving: 'Saving...',
    deleting: 'Deleting...',
    processing: 'Processing...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information',
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed'
  },
  navigation: {
    dashboard: 'Dashboard',
    analytics: 'Analytics',
    insights: 'Insights',
    reports: 'Reports',
    settings: 'Settings',
    profile: 'Profile',
    help: 'Help',
    logout: 'Logout'
  },
  time: {
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    lastWeek: 'Last Week',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    thisYear: 'This Year',
    custom: 'Custom Range',
    from: 'From',
    to: 'To'
  },
  messages: {
    welcome: 'Welcome back, {name}!',
    confirmDelete: 'Are you sure you want to delete {item}?',
    noData: 'No data available',
    noResults: 'No results found',
    itemsSelected: '{count} items selected',
    page: 'Page {current} of {total}'
  }
} as const;
```

## Key Naming Conventions

### 1. General Rules
- Use **camelCase** for all keys
- Keep keys descriptive but concise
- Group related translations under common parent keys
- Use singular forms for keys (e.g., `user` not `users`)

### 2. Naming Patterns

```typescript
// Feature-based grouping
dashboard.metrics.revenue = "Revenue"
dashboard.metrics.orders = "Orders"
dashboard.filters.dateRange = "Date Range"

// Action-based grouping
actions.create.restaurant = "Create Restaurant"
actions.edit.menu = "Edit Menu"
actions.delete.confirmation = "Are you sure?"

// Component-based grouping
components.chart.title = "Analytics Overview"
components.table.emptyState = "No data to display"
components.form.validation.required = "This field is required"

// Page-specific content
pages.auth.login.title = "Sign In"
pages.auth.login.subtitle = "Welcome back to BiteBase"
pages.settings.account.header = "Account Settings"
```

### 3. Special Cases

```typescript
// Pluralization (use function with count parameter)
messages.items = (count: number) => count === 1 ? '1 item' : `${count} items`

// Dynamic content (use template literals)
messages.greeting = 'Hello, {name}!'
messages.dateRange = 'From {startDate} to {endDate}'

// Context-specific translations
button.save = 'Save'              // Generic
form.button.save = 'Save Form'    // Form-specific
modal.button.save = 'Save & Close' // Modal-specific
```

## Type Safety

### Translation Types Definition

```typescript
// types.ts
export interface TranslationSchema {
  common: typeof import('./en/common').common;
  auth: typeof import('./en/auth').auth;
  dashboard: typeof import('./en/dashboard').dashboard;
  analytics: typeof import('./en/analytics').analytics;
  insights: typeof import('./en/insights').insights;
  location: typeof import('./en/location').location;
  restaurant: typeof import('./en/restaurant').restaurant;
  campaign: typeof import('./en/campaign').campaign;
  pos: typeof import('./en/pos').pos;
  price: typeof import('./en/price').price;
  product: typeof import('./en/product').product;
  promotion: typeof import('./en/promotion').promotion;
  reports: typeof import('./en/reports').reports;
  settings: typeof import('./en/settings').settings;
  errors: typeof import('./en/errors').errors;
  validation: typeof import('./en/validation').validation;
  components: {
    charts: typeof import('./en/components/charts').charts;
    tables: typeof import('./en/components/tables').tables;
    forms: typeof import('./en/components/forms').forms;
    modals: typeof import('./en/components/modals').modals;
  };
}

export type TranslationKey = keyof TranslationSchema;
export type Language = 'en' | 'th';
```

## Dynamic Loading Strategy

### 1. Lazy Loading Implementation

```typescript
// utils.ts
import { Language, TranslationSchema } from './types';

const translationCache = new Map<string, Promise<any>>();

export async function loadTranslation(
  language: Language,
  namespace: keyof TranslationSchema
): Promise<TranslationSchema[typeof namespace]> {
  const cacheKey = `${language}.${namespace}`;
  
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey)!;
  }
  
  const loadPromise = import(`./${language}/${namespace}.ts`)
    .then(module => module[namespace])
    .catch(() => {
      // Fallback to English if translation not found
      if (language !== 'en') {
        return import(`./en/${namespace}.ts`).then(m => m[namespace]);
      }
      throw new Error(`Translation not found: ${cacheKey}`);
    });
  
  translationCache.set(cacheKey, loadPromise);
  return loadPromise;
}
```

### 2. Updated Language Context

```typescript
// Enhanced LanguageContext with dynamic loading
export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>('en');
  const [translations, setTranslations] = useState<Partial<TranslationSchema>>({});
  const [loadedNamespaces, setLoadedNamespaces] = useState<Set<string>>(new Set());

  // Preload common translations
  useEffect(() => {
    loadNamespace('common');
    loadNamespace('navigation');
  }, [language]);

  const loadNamespace = async (namespace: keyof TranslationSchema) => {
    const key = `${language}.${namespace}`;
    if (loadedNamespaces.has(key)) return;

    try {
      const translation = await loadTranslation(language, namespace);
      setTranslations(prev => ({
        ...prev,
        [namespace]: translation
      }));
      setLoadedNamespaces(prev => new Set(prev).add(key));
    } catch (error) {
      console.error(`Failed to load translation: ${key}`, error);
    }
  };

  const t = (key: string, params?: Record<string, any>): string => {
    const keys = key.split('.');
    const namespace = keys[0] as keyof TranslationSchema;
    
    // Auto-load namespace if not loaded
    if (!loadedNamespaces.has(`${language}.${namespace}`)) {
      loadNamespace(namespace);
      return key; // Return key while loading
    }

    // Rest of the translation logic...
  };

  // ... rest of implementation
}
```

## Migration Strategy

### Phase 1: Setup Infrastructure (Week 1)
1. Create the folder structure under `frontend/src/locales/`
2. Set up TypeScript types and interfaces
3. Implement utility functions for translation loading
4. Create base translation files with common translations

### Phase 2: Extract Existing Translations (Week 1-2)
1. Move existing translations from LanguageContext.tsx to appropriate files:
   - `common.ts` - Current common translations
   - `navigation.ts` - Current nav translations
2. Update imports and references
3. Test existing functionality remains intact

### Phase 3: Expand Translation Coverage (Week 2-3)
1. Audit all pages and components for hardcoded strings
2. Create translation files for each feature module
3. Replace hardcoded strings with translation keys
4. Prioritize by feature importance:
   - Authentication flows
   - Dashboard and core features
   - Analytics and insights
   - Settings and configuration

### Phase 4: Component Translations (Week 3-4)
1. Create component-specific translation files
2. Update reusable components to use translations
3. Ensure consistent terminology across components

### Phase 5: Optimization & Testing (Week 4)
1. Implement lazy loading for translation files
2. Add translation key validation
3. Create unit tests for translation system
4. Performance testing with large translation files

## Performance Considerations

### 1. Code Splitting
- Each translation file is a separate module
- Lazy load translations based on route/feature
- Preload critical translations (common, nav)

### 2. Caching Strategy
- Cache loaded translations in memory
- Use localStorage for offline support
- Implement cache invalidation on language change

### 3. Bundle Size Optimization
- Split large translation files by sub-features
- Use dynamic imports for rarely used translations
- Consider CDN hosting for translation files

## Missing Translation Handling

### 1. Fallback Chain
```typescript
1. Requested translation in current language
2. Same key in English (if current language is not English)
3. Return the key itself (for debugging)
4. Log warning in development mode
```

### 2. Development Tools
- Console warnings for missing translations
- Translation coverage report generator
- VS Code extension for translation key autocomplete

## Best Practices

### 1. Translation Guidelines
- Keep translations concise and clear
- Maintain consistent terminology
- Consider text expansion (Thai text can be longer)
- Use proper formatting for numbers, dates, and currencies

### 2. Component Integration
```typescript
// Good: Use translation hook
const { t } = useTranslation();
return <h1>{t('dashboard.title')}</h1>;

// Better: With namespace
const { t } = useTranslation('dashboard');
return <h1>{t('title')}</h1>;

// Best: With TypeScript autocomplete
const { t } = useTranslation<'dashboard'>();
return <h1>{t('metrics.revenue')}</h1>;
```

### 3. Dynamic Content
```typescript
// Template literals
t('messages.welcome', { name: user.name })
// Output: "Welcome back, John!"

// Pluralization
t('messages.items', { count: 5 })
// Output: "5 items"

// Date formatting
t('common.dateRange', { 
  start: formatDate(startDate), 
  end: formatDate(endDate) 
})
```

## Quality Assurance

### 1. Translation Validation
- Automated checks for missing translations
- Consistency validation across languages
- Character length warnings for UI constraints

### 2. Testing Strategy
- Unit tests for translation functions
- Integration tests for language switching
- E2E tests in both languages
- Visual regression tests for UI layout

### 3. Translation Workflow
- Use translation keys in development
- Professional translation service integration
- Review process for translation quality
- Version control for translation changes

## Future Enhancements

1. **Additional Languages**: Structure supports easy addition of new languages
2. **Translation Management System**: Integration with services like Crowdin or Lokalise
3. **A/B Testing**: Support for translation variants
4. **Context-Aware Translations**: Different translations based on user role or context
5. **Machine Translation**: Automatic translation for missing keys in development