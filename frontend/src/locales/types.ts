// TypeScript types for translation system

export interface TranslationSchema {
  common: typeof import('./en/common').common;
  navigation: typeof import('./en/navigation').navigation;
  auth: typeof import('./en/auth').auth;
  landing: typeof import('./en/landing').landing;
  // Add more namespaces as needed
}

export type TranslationKey = keyof TranslationSchema;
export type Language = 'en' | 'th';