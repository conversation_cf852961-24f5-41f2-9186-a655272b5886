// Main export for translation system
import type { Language, TranslationSchema, TranslationKey } from './types';

const translationCache = new Map<string, Promise<any>>();

export async function loadTranslation(
  language: Language,
  namespace: TranslationKey
): Promise<TranslationSchema[typeof namespace]> {
  const cacheKey = `${language}.${namespace}`;
  if (translationCache.has(cacheKey)) {
    return translationCache.get(cacheKey)!;
  }
  const loadPromise = import(`./${language}/${namespace}.ts`)
    .then(module => module[namespace])
    .catch(() => {
      // Fallback to English if translation not found
      if (language !== 'en') {
        return import(`./en/${namespace}.ts`).then(m => m[namespace]);
      }
      throw new Error(`Translation not found: ${cacheKey}`);
    });
  translationCache.set(cacheKey, loadPromise);
  return loadPromise;
}