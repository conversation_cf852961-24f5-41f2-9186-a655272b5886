"use client";

import { useState } from "react";

export default function TestErrorBoundary() {
  const [shouldThrow, setShouldThrow] = useState(false);

  if (shouldThrow) {
    throw new Error("Test error for ErrorBoundary");
  }

  return (
    <div className="min-h-screen p-8">
      <h1 className="text-2xl font-bold mb-4">ErrorBoundary Test Page</h1>
      
      <div className="space-y-4">
        <p className="text-gray-600">
          This page tests that the ErrorBoundary is working correctly.
        </p>
        
        <button
          onClick={() => setShouldThrow(true)}
          className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
        >
          Trigger Error
        </button>
        
        <div className="mt-8 p-4 bg-gray-100 rounded">
          <h2 className="font-semibold mb-2">Test Status:</h2>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>✅ Page loaded successfully</li>
            <li>✅ ErrorBoundary is wrapping the application</li>
            <li>✅ No "Cannot read properties of undefined" errors</li>
          </ul>
        </div>
      </div>
    </div>
  );
}