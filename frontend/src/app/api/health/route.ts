import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Check if backend is reachable
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
    const response = await fetch(`${backendUrl}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (response.ok) {
      const backendHealth = await response.json()
      return NextResponse.json({
        status: 'healthy',
        service: 'bitebase-intelligence-frontend',
        version: '1.0.0',
        backend: backendHealth,
        timestamp: new Date().toISOString()
      })
    } else {
      return NextResponse.json({
        status: 'degraded',
        service: 'bitebase-intelligence-frontend',
        version: '1.0.0',
        backend: { status: 'unreachable', error: 'Backend API not responding' },
        timestamp: new Date().toISOString()
      }, { status: 503 })
    }
  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      service: 'bitebase-intelligence-frontend',
      version: '1.0.0',
      backend: { status: 'error', error: (error as Error).message },
      timestamp: new Date().toISOString()
    }, { status: 503 })
  }
}
