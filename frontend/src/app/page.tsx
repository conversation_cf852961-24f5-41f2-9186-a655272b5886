'use client'

import React from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
// import PricingSection from '@/components/landing/PricingSection'
import {
  <PERSON>rkles,
  Rocket,
  BarChart3,
  MapPin,
  Brain,
  Star,
  Users,
  ChefHat,
  Coffee,
  Utensils,
  Pizza,
  Cookie,
  ArrowRight,
  Play,
  CheckCircle,
  Globe,
  Shield,
  Zap as Lightning,
  TrendingUp,
  Target,
  Smartphone,
  Monitor,
  Tablet,
  Eye,
  Heart,
  Cpu,
  Database,
  Cloud
} from 'lucide-react'

// Sophisticated Floating Elements with Framer Motion
const FloatingElements = () => {
  const foodIcons = [ChefHat, Coffee, Utensils, Pizza, Cookie]
  const techIcons = [Cpu, Database, Cloud, Brain, BarChart3]

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Food Icons with sophisticated animations */}
      {foodIcons.map((Icon, index) => (
        <motion.div
          key={`food-${index}`}
          className="absolute text-orange-400/20"
          initial={{
            x: Math.random() * 1200,
            y: Math.random() * 800,
            rotate: 0,
            scale: 0.5
          }}
          animate={{
            x: Math.random() * 1200,
            y: Math.random() * 800,
            rotate: 360,
            scale: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 15 + index * 2,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
            delay: index * 0.5
          }}
          style={{
            left: `${15 + index * 20}%`,
            top: `${20 + index * 15}%`,
          }}
        >
          <Icon size={24 + index * 4} />
        </motion.div>
      ))}

      {/* Tech Icons with different animation pattern */}
      {techIcons.map((Icon, index) => (
        <motion.div
          key={`tech-${index}`}
          className="absolute text-blue-400/20"
          initial={{
            x: Math.random() * 1200,
            y: Math.random() * 800,
            opacity: 0.3
          }}
          animate={{
            x: Math.random() * 1200,
            y: Math.random() * 800,
            opacity: [0.3, 0.7, 0.3]
          }}
          transition={{
            duration: 12 + index * 3,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "linear",
            delay: index * 0.8
          }}
          style={{
            right: `${10 + index * 15}%`,
            bottom: `${15 + index * 20}%`,
          }}
        >
          <Icon size={20 + index * 3} />
        </motion.div>
      ))}
    </div>
  )
}

// Sophisticated Animated Counter
const AnimatedCounter = ({ end, duration = 2000, suffix = "" }: { end: number; duration?: number; suffix?: string }) => {
  const [count, setCount] = React.useState(0)

  React.useEffect(() => {
    let startTime: number
    let animationFrame: number

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)

      setCount(Math.floor(progress * end))

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)
    return () => cancelAnimationFrame(animationFrame)
  }, [end, duration])

  return (
    <motion.span
      initial={{ scale: 0.5, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="font-bold text-4xl md:text-5xl bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent"
    >
      {count.toLocaleString()}{suffix}
    </motion.span>
  )
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-orange-50/30 to-red-50/30">
      {/* Hero Section with sophisticated animations */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <FloatingElements />

        <div className="relative z-10 max-w-7xl mx-auto px-6 py-12 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="space-y-8"
          >
            <motion.h1
              className="text-5xl md:text-7xl font-bold mb-6"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              <span className="bg-gradient-to-r from-gray-900 via-orange-600 to-red-600 bg-clip-text text-transparent">
                BiteBase
              </span>
              <br />
              <motion.span
                className="text-orange-500"
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
              >
                Intelligence
              </motion.span>
            </motion.h1>

            <motion.p
              className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.7 }}
            >
              AI-Powered Business Intelligence Platform for Restaurant & Cafe Industry
            </motion.p>
          </motion.div>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button size="lg" className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-8 py-4 text-lg rounded-full shadow-lg">
                <Play className="mr-2 h-5 w-5" />
                Start Free Trial
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button variant="outline" size="lg" className="border-2 border-orange-500 text-orange-500 hover:bg-orange-50 px-8 py-4 text-lg rounded-full">
                <ArrowRight className="mr-2 h-5 w-5" />
                Watch Demo
              </Button>
            </motion.div>
          </motion.div>

          {/* Stats Section with animated counters */}
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.1 }}
          >
            <div className="text-center">
              <AnimatedCounter end={10000} suffix="+" />
              <p className="text-gray-600 mt-2">Restaurants</p>
            </div>
            <div className="text-center">
              <AnimatedCounter end={95} suffix="%" />
              <p className="text-gray-600 mt-2">Accuracy</p>
            </div>
            <div className="text-center">
              <AnimatedCounter end={50} suffix="M+" />
              <p className="text-gray-600 mt-2">Data Points</p>
            </div>
            <div className="text-center">
              <AnimatedCounter end={24} suffix="/7" />
              <p className="text-gray-600 mt-2">Support</p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-orange-600 bg-clip-text text-transparent">
              Powerful Features
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to make data-driven decisions for your restaurant business
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: BarChart3,
                title: "AI Market Intelligence",
                description: "Advanced analytics and market insights powered by artificial intelligence",
                gradient: "from-blue-500 to-purple-500"
              },
              {
                icon: MapPin,
                title: "Location Intelligence",
                description: "Geospatial analysis and location-based business intelligence",
                gradient: "from-green-500 to-teal-500"
              },
              {
                icon: Brain,
                title: "Predictive Analytics",
                description: "Machine learning models for forecasting and trend analysis",
                gradient: "from-orange-500 to-red-500"
              },
              {
                icon: Users,
                title: "Customer Insights",
                description: "Deep understanding of customer behavior and preferences",
                gradient: "from-purple-500 to-pink-500"
              },
              {
                icon: TrendingUp,
                title: "Real-time Analytics",
                description: "Live data processing and real-time business metrics",
                gradient: "from-cyan-500 to-blue-500"
              },
              {
                icon: Shield,
                title: "Enterprise Security",
                description: "Bank-level security with compliance and data protection",
                gradient: "from-gray-600 to-gray-800"
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 overflow-hidden h-full"
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10, scale: 1.02 }}
              >
                <div className="p-6 md:p-8 h-full flex flex-col space-y-6">
                  <div className="flex-shrink-0">
                    <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${feature.gradient} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  <div className="flex-grow space-y-4">
                    <h3 className="text-xl font-bold text-gray-900 leading-tight">{feature.title}</h3>
                    <p className="text-gray-600 leading-relaxed text-base">{feature.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gradient-to-br from-gray-50 to-orange-50/30">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-orange-600 bg-clip-text text-transparent">
              Choose Your Plan
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Flexible pricing options designed to grow with your business
            </p>
          </motion.div>

          {/* Simple Pricing Cards */}
          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                name: "Starter",
                price: "$39",
                description: "Perfect for small restaurants",
                features: ["Basic Analytics", "Location Intelligence", "Email Support", "5 Locations"]
              },
              {
                name: "Professional",
                price: "$99",
                description: "Ideal for growing businesses",
                features: ["Advanced Analytics", "AI Insights", "Priority Support", "25 Locations", "Custom Reports"],
                popular: true
              },
              {
                name: "Enterprise",
                price: "$319",
                description: "For large restaurant chains",
                features: ["Full Analytics Suite", "Custom AI Models", "24/7 Support", "Unlimited Locations", "API Access"]
              }
            ].map((plan, index) => (
              <motion.div
                key={index}
                className={`relative rounded-2xl border-2 ${plan.popular ? 'border-orange-500 bg-orange-50' : 'border-gray-200 bg-white'} shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden`}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -10 }}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <span className="bg-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <div className="p-8 space-y-6">
                  <div className="text-center space-y-4">
                    <h3 className="text-2xl font-bold text-gray-900">{plan.name}</h3>
                    <div className="space-y-2">
                      <div className="text-4xl font-bold text-orange-500">{plan.price}<span className="text-lg text-gray-500">/month</span></div>
                      <p className="text-gray-600">{plan.description}</p>
                    </div>
                  </div>
                  <ul className="space-y-4">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <div className="pt-4">
                    <Button
                      className={`w-full ${plan.popular ? 'bg-orange-500 hover:bg-orange-600' : 'bg-gray-900 hover:bg-gray-800'} text-white`}
                      size="lg"
                    >
                      Get Started
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-orange-500 to-red-500">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Ready to Transform Your Business?
            </h2>
            <p className="text-xl text-orange-100 mb-8 max-w-2xl mx-auto">
              Join thousands of restaurants already using BiteBase Intelligence to make smarter decisions
            </p>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button size="lg" className="bg-white text-orange-500 hover:bg-gray-100 px-8 py-4 text-lg rounded-full shadow-lg">
                <Rocket className="mr-2 h-5 w-5" />
                Start Your Free Trial
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>

  )
}
