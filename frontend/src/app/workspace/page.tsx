'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useSearchParams } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import WorkspaceHub from '@/components/workspace/WorkspaceHub';
import { useTranslation } from '@/hooks/useTranslation';

export default function WorkspacePage() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const setupRequired = searchParams.get('setup') === 'required';

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
        <div className="container mx-auto px-6 py-8">
          {/* Header Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
              {t('workspace.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('workspace.subtitle')}
            </p>
            
            {setupRequired && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
                className="mt-6 p-4 bg-orange-100 border border-orange-200 rounded-lg max-w-2xl mx-auto"
              >
                <p className="text-orange-800">
                  {t('workspace.setupRequired')}
                </p>
              </motion.div>
            )}
          </motion.div>

          {/* Workspace Hub Component */}
          <WorkspaceHub setupRequired={setupRequired} />
        </div>
      </div>
    </MainLayout>
  );
}