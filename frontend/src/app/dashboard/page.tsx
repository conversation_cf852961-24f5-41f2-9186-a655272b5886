'use client'

import React from 'react'
import MainLayout from '@/components/layout/MainLayout'
import EnhancedDashboard from '@/components/dashboard/EnhancedDashboard'
import { useLanguage } from '@/contexts/LanguageContext'

export default function DashboardPage() {
  const { t } = useLanguage()
  return (
    <MainLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-primary font-bold text-foreground">{t('dashboard.title')}</h1>
          <p className="text-muted-foreground font-secondary">
            {t('dashboard.welcome')}
          </p>
        </div>
        <EnhancedDashboard />
      </div>
    </MainLayout>
  )
}