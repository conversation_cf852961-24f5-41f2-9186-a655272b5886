'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/contexts/LanguageContext'
import {
  Check,
  Crown,
  Rocket,
  Building,
  Sparkles,
  Zap,
  Shield,
  Users,
  BarChart3,
  Brain,
  MapPin,
  Star,
  ArrowRight
} from 'lucide-react'

const PricingSection = () => {
  const [isAnnual, setIsAnnual] = useState(true)
  const { t } = useLanguage()

  const plans = [
    {
      name: t('pricing.starter'),
      description: t('pricing.starterDesc'),
      icon: Rocket,
      price: { monthly: 49, annual: 39 },
      originalPrice: { monthly: 69, annual: 55 },
      popular: false,
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'from-blue-50 to-cyan-50',
      features: [
        t('pricing.realtimeAnalytics'),
        t('pricing.basicLocationIntelligence'),
        t('pricing.upTo1000Orders'),
        t('pricing.emailSupport'),
        t('pricing.mobileAppAccess'),
        t('pricing.basicReporting'),
        t('pricing.dataExport'),
        t('pricing.standardIntegrations')
      ],
      limitations: [
        t('pricing.limitedTo1Location'),
        t('pricing.basicAiInsightsOnly')
      ]
    },
    {
      name: t('pricing.professional'),
      description: t('pricing.professionalDesc'),
      icon: Crown,
      price: { monthly: 149, annual: 119 },
      originalPrice: { monthly: 199, annual: 159 },
      popular: true,
      color: 'from-orange-500 to-red-500',
      bgColor: 'from-orange-50 to-red-50',
      features: [
        t('pricing.everythingInStarter'),
        t('pricing.advancedAiInsights'),
        t('pricing.multiLocationManagement'),
        t('pricing.upTo10000Orders'),
        t('pricing.prioritySupport'),
        t('pricing.customDashboards'),
        t('pricing.advancedAnalytics'),
        t('pricing.apiAccess'),
        t('pricing.whiteLabelOptions'),
        t('pricing.predictiveAnalytics'),
        t('pricing.competitorAnalysis'),
        t('pricing.customIntegrations')
      ],
      limitations: [
        t('pricing.upTo5Locations')
      ]
    },
    {
      name: t('pricing.enterprise'),
      description: t('pricing.enterpriseDesc'),
      icon: Building,
      price: { monthly: 399, annual: 319 },
      originalPrice: { monthly: 499, annual: 399 },
      popular: false,
      color: 'from-purple-500 to-pink-500',
      bgColor: 'from-purple-50 to-pink-50',
      features: [
        t('pricing.everythingInProfessional'),
        t('pricing.unlimitedLocations'),
        t('pricing.unlimitedOrders'),
        t('pricing.dedicatedAccountManager'),
        t('pricing.phoneSupport'),
        t('pricing.customAiModels'),
        t('pricing.advancedSecurity'),
        t('pricing.slaGuarantee'),
        t('pricing.onPremiseDeployment'),
        t('pricing.customTraining'),
        t('pricing.advancedIntegrations'),
        t('pricing.realtimeCollaboration'),
        t('pricing.enterpriseSso'),
        t('pricing.complianceTools')
      ],
      limitations: []
    }
  ]

  const additionalFeatures = [
    { icon: Shield, title: t('pricing.enterpriseSecurity'), description: t('pricing.soc2Certified') },
    { icon: Zap, title: t('pricing.uptime'), description: t('pricing.guaranteedAvailability') },
    { icon: Users, title: t('pricing.expertSupport'), description: t('pricing.customerSuccess') },
    { icon: Brain, title: t('pricing.aiPowered'), description: t('pricing.mlInsights') }
  ]

  return (
    <section id="pricing" className="relative py-32 bg-gradient-to-br from-gray-50 via-white to-gray-100 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-orange-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
      </div>

      <div className="max-w-7xl mx-auto px-6 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="inline-flex items-center space-x-2 bg-orange-100 text-orange-600 px-6 py-3 rounded-full font-semibold mb-8"
          >
            <Sparkles className="h-5 w-5" />
            <span>{t('pricing.limitedTimeOffer')}</span>
          </motion.div>

          <h2 className="text-5xl md:text-7xl font-black text-gray-900 mb-8 leading-tight">
            {t('pricing.chooseYour')}{" "}
            <motion.span
              className="text-orange-500"
              animate={{ scale: [1, 1.05, 1] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            >
              {t('pricing.success')}
            </motion.span>{" "}
            {t('pricing.plan')}
          </h2>
          <p className="text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            {t('pricing.trialDesc')}
          </p>

          {/* Billing Toggle */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="flex items-center justify-center mt-12 space-x-4"
          >
            <span className={`text-lg font-medium ${!isAnnual ? 'text-gray-900' : 'text-gray-500'}`}>
              Monthly
            </span>
            <motion.button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative w-16 h-8 rounded-full transition-colors duration-300 ${
                isAnnual ? 'bg-orange-500' : 'bg-gray-300'
              }`}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                className="absolute top-1 w-6 h-6 bg-white rounded-full shadow-md"
                animate={{ x: isAnnual ? 32 : 4 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              />
            </motion.button>
            <span className={`text-lg font-medium ${isAnnual ? 'text-gray-900' : 'text-gray-500'}`}>
              Annual
            </span>
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: isAnnual ? 1 : 0, scale: isAnnual ? 1 : 0 }}
              transition={{ duration: 0.3 }}
              className="bg-green-100 text-green-600 px-3 py-1 rounded-full text-sm font-bold"
            >
              Save 20%
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
              whileHover={{ y: -10, scale: 1.02 }}
              className={`relative bg-white rounded-3xl shadow-2xl border-2 overflow-hidden ${
                plan.popular ? 'border-orange-500 scale-105' : 'border-gray-200'
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                >
                  <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-2 rounded-full font-bold text-sm flex items-center space-x-2">
                    <Star className="h-4 w-4 fill-current" />
                    <span>Most Popular</span>
                  </div>
                </motion.div>
              )}

              {/* Background Gradient */}
              <motion.div
                className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${plan.color} opacity-10 rounded-full -translate-y-16 translate-x-16`}
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              />

              <div className="relative z-10 p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <motion.div
                    className={`w-16 h-16 bg-gradient-to-br ${plan.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}
                    whileHover={{ rotate: 360 }}
                    transition={{ duration: 0.6 }}
                  >
                    <plan.icon className="h-8 w-8 text-white" />
                  </motion.div>
                  <h3 className="text-2xl font-black text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 mb-6">{plan.description}</p>

                  {/* Pricing */}
                  <div className="mb-6">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <span className="text-gray-400 line-through text-lg">
                        ${isAnnual ? plan.originalPrice.annual : plan.originalPrice.monthly}
                      </span>
                      <div className="bg-green-100 text-green-600 px-2 py-1 rounded text-xs font-bold">
                        30% OFF
                      </div>
                    </div>
                    <div className="flex items-baseline justify-center">
                      <span className="text-5xl font-black text-gray-900">
                        ${isAnnual ? plan.price.annual : plan.price.monthly}
                      </span>
                      <span className="text-gray-600 ml-2">
                        /{isAnnual ? 'month' : 'month'}
                      </span>
                    </div>
                    {isAnnual && (
                      <p className="text-sm text-gray-500 mt-2">
                        Billed annually (${plan.price.annual * 12}/year)
                      </p>
                    )}
                  </div>

                  {/* CTA Button */}
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      className={`w-full py-4 text-lg font-bold rounded-full ${
                        plan.popular
                          ? 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white'
                          : 'bg-gray-900 hover:bg-gray-800 text-white'
                      }`}
                    >
                      {t('pricing.startFreeTrial')}
                      <ArrowRight className="h-5 w-5 ml-2" />
                    </Button>
                  </motion.div>
                </div>

                {/* Features */}
                <div className="space-y-4">
                  <h4 className="font-bold text-gray-900 text-lg">{t('pricing.whatsIncluded')}</h4>
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <motion.li
                        key={featureIndex}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: featureIndex * 0.05 }}
                        viewport={{ once: true }}
                        className="flex items-center space-x-3"
                      >
                        <div className="flex-shrink-0">
                          <Check className="h-5 w-5 text-green-500" />
                        </div>
                        <span className="text-gray-700">{feature}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional Features */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <h3 className="text-3xl font-bold text-gray-900 mb-12">
            {t('pricing.whyChoose')}
          </h3>
          <div className="grid md:grid-cols-4 gap-8">
            {additionalFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="text-center"
              >
                <motion.div
                  className="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-4"
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  <feature.icon className="h-8 w-8 text-white" />
                </motion.div>
                <h4 className="text-xl font-bold text-gray-900 mb-2">{feature.title}</h4>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Money Back Guarantee */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="text-center mt-20 bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl p-12"
        >
          <div className="flex items-center justify-center mb-6">
            <motion.div
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center"
            >
              <Shield className="h-10 w-10 text-white" />
            </motion.div>
          </div>
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            {t('pricing.moneyBackGuarantee')}
          </h3>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('pricing.moneyBackDesc')}
          </p>
        </motion.div>
      </div>
    </section>
  )
}

export default PricingSection
