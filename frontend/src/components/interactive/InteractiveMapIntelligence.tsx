'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Map,
  MapPin,
  Users,
  DollarSign,
  TrendingUp,
  Target,
  Filter,
  Layers,
  Search,
  Navigation,
  Home,
  Building,
  Car,
  Clock,
  Zap,
  Eye,
  BarChart3
} from 'lucide-react';

interface LocationData {
  id: string;
  name: string;
  coordinates: [number, number];
  type: 'restaurant' | 'competitor' | 'opportunity';
  rating: number;
  footTraffic: number;
  avgSpend: number;
  demographics: {
    age: string;
    income: string;
    lifestyle: string;
  };
}

interface MapLayer {
  id: string;
  name: string;
  icon: React.ElementType;
  color: string;
  active: boolean;
}

export default function InteractiveMapIntelligence() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [mapLayers, setMapLayers] = useState<MapLayer[]>([
    { id: 'traffic', name: 'Foot Traffic', icon: Users, color: 'blue', active: true },
    { id: 'demographics', name: 'Demographics', icon: Target, color: 'green', active: false },
    { id: 'competitors', name: 'Competitors', icon: Building, color: 'red', active: true },
    { id: 'opportunities', name: 'Opportunities', icon: Zap, color: 'yellow', active: false }
  ]);

  const mockLocations: LocationData[] = [
    {
      id: '1',
      name: 'Your Restaurant',
      coordinates: [-74.006, 40.7128],
      type: 'restaurant',
      rating: 4.2,
      footTraffic: 850,
      avgSpend: 45,
      demographics: { age: '25-35', income: '$50k-75k', lifestyle: 'Urban Professional' }
    },
    {
      id: '2',
      name: 'Competitor A',
      coordinates: [-74.008, 40.7118],
      type: 'competitor',
      rating: 4.5,
      footTraffic: 1200,
      avgSpend: 55,
      demographics: { age: '30-40', income: '$75k-100k', lifestyle: 'Family Oriented' }
    },
    {
      id: '3',
      name: 'Growth Opportunity',
      coordinates: [-74.004, 40.7138],
      type: 'opportunity',
      rating: 0,
      footTraffic: 600,
      avgSpend: 35,
      demographics: { age: '22-30', income: '$35k-50k', lifestyle: 'Student/Young Professional' }
    }
  ];

  const tabs = [
    { id: 'overview', label: 'Location Overview', icon: Map },
    { id: 'analysis', label: 'Site Analysis', icon: BarChart3 },
    { id: 'opportunities', label: 'Opportunities', icon: Target },
    { id: 'competition', label: 'Competition', icon: Building }
  ];

  const toggleLayer = (layerId: string) => {
    setMapLayers(layers =>
      layers.map(layer =>
        layer.id === layerId ? { ...layer, active: !layer.active } : layer
      )
    );
  };

  const insights = [
    {
      title: 'High-Traffic Opportunity',
      description: '2 blocks north shows 40% higher foot traffic',
      impact: 'high',
      metric: '+$2,400/month',
      icon: TrendingUp
    },
    {
      title: 'Demographic Match',
      description: 'Target demographic concentration in west area',
      impact: 'medium',
      metric: '85% match',
      icon: Users
    },
    {
      title: 'Competition Gap',
      description: 'Underserved area with limited dining options',
      impact: 'high',
      metric: '3 mile radius',
      icon: Target
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          Interactive Map Intelligence
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Location-based analytics and demographic insights for optimal restaurant placement
        </p>
      </motion.div>

      {/* Tab Navigation */}
      <div className="flex justify-center">
        <div className="bg-white rounded-xl border border-gray-200 p-2 shadow-sm">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-green-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="grid lg:grid-cols-4 gap-8">
        {/* Map Controls Sidebar */}
        <div className="space-y-6">
          {/* Layer Controls */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
              <Layers className="h-5 w-5 mr-2" />
              Map Layers
            </h3>
            <div className="space-y-3">
              {mapLayers.map((layer) => (
                <motion.div
                  key={layer.id}
                  whileHover={{ scale: 1.02 }}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <layer.icon className={`h-5 w-5 text-${layer.color}-500`} />
                    <span className="text-sm font-medium text-gray-700">
                      {layer.name}
                    </span>
                  </div>
                  <button
                    onClick={() => toggleLayer(layer.id)}
                    className={`w-10 h-6 rounded-full transition-colors ${
                      layer.active ? 'bg-green-500' : 'bg-gray-300'
                    }`}
                  >
                    <div
                      className={`w-4 h-4 bg-white rounded-full transition-transform ${
                        layer.active ? 'translate-x-5' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Search & Filter */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
              <Search className="h-5 w-5 mr-2" />
              Search Location
            </h3>
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Enter address or area..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
              <button className="w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg font-semibold transition-colors">
                Analyze Location
              </button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              Area Stats
            </h3>
            <div className="space-y-4">
              {[
                { label: 'Avg Foot Traffic', value: '1.2K/day', icon: Users },
                { label: 'Median Income', value: '$65K', icon: DollarSign },
                { label: 'Competition', value: '8 restaurants', icon: Building },
                { label: 'Growth Rate', value: '+12%', icon: TrendingUp }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <stat.icon className="h-5 w-5 text-green-500" />
                    <span className="text-gray-600 text-sm">{stat.label}</span>
                  </div>
                  <span className="font-bold text-gray-800">{stat.value}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Map Area */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
            {/* Map Header */}
            <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-4 text-white">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold">Interactive Location Map</h3>
                <div className="flex items-center space-x-2">
                  <button className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                    <Navigation className="h-5 w-5" />
                  </button>
                  <button className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                    <Eye className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Mock Map Display */}
            <div className="h-96 bg-gradient-to-br from-green-50 to-blue-50 relative flex items-center justify-center">
              {/* Placeholder for actual map integration */}
              <div className="text-center">
                <Map className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h4 className="text-xl font-bold text-gray-800 mb-2">
                  Interactive Map View
                </h4>
                <p className="text-gray-600 mb-6">
                  Real map with Mapbox/Google Maps integration would be displayed here
                </p>
                
                {/* Mock location markers */}
                <div className="flex justify-center space-x-8">
                  {mockLocations.map((location, index) => (
                    <motion.div
                      key={location.id}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: index * 0.2 }}
                      whileHover={{ scale: 1.1 }}
                      onClick={() => setSelectedLocation(location)}
                      className={`w-12 h-12 rounded-full flex items-center justify-center cursor-pointer shadow-lg ${
                        location.type === 'restaurant' ? 'bg-blue-500' :
                        location.type === 'competitor' ? 'bg-red-500' :
                        'bg-yellow-500'
                      }`}
                    >
                      <MapPin className="h-6 w-6 text-white" />
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Selected Location Details */}
          {selectedLocation && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 bg-white rounded-xl border border-gray-200 p-6 shadow-sm"
            >
              <h4 className="text-lg font-bold text-gray-800 mb-4">
                {selectedLocation.name}
              </h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-500">
                    {selectedLocation.rating || 'N/A'}
                  </div>
                  <div className="text-sm text-gray-600">Rating</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-500">
                    {selectedLocation.footTraffic}
                  </div>
                  <div className="text-sm text-gray-600">Daily Traffic</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-500">
                    ${selectedLocation.avgSpend}
                  </div>
                  <div className="text-sm text-gray-600">Avg Spend</div>
                </div>
              </div>
            </motion.div>
          )}
        </div>

        {/* Insights Sidebar */}
        <div className="space-y-6">
          {/* AI Insights */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
              <Zap className="h-5 w-5 mr-2 text-yellow-500" />
              AI Insights
            </h3>
            <div className="space-y-4">
              {insights.map((insight, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-4 rounded-lg border-l-4 ${
                    insight.impact === 'high' ? 'bg-red-50 border-red-500' :
                    'bg-yellow-50 border-yellow-500'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <insight.icon className={`h-5 w-5 mt-1 ${
                      insight.impact === 'high' ? 'text-red-500' : 'text-yellow-500'
                    }`} />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 text-sm mb-1">
                        {insight.title}
                      </h4>
                      <p className="text-gray-600 text-xs mb-2">
                        {insight.description}
                      </p>
                      <div className={`text-sm font-bold ${
                        insight.impact === 'high' ? 'text-red-600' : 'text-yellow-600'
                      }`}>
                        {insight.metric}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Demographics */}
          <div className="bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl p-6 text-white">
            <h3 className="text-lg font-bold mb-4 flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Area Demographics
            </h3>
            <div className="space-y-3">
              <div>
                <div className="text-sm text-purple-100">Primary Age Group</div>
                <div className="text-xl font-bold">25-35 years</div>
              </div>
              <div>
                <div className="text-sm text-purple-100">Median Income</div>
                <div className="text-xl font-bold">$50K-$75K</div>
              </div>
              <div>
                <div className="text-sm text-purple-100">Lifestyle</div>
                <div className="text-xl font-bold">Urban Professional</div>
              </div>
            </div>
          </div>

          {/* Action Items */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              Recommended Actions
            </h3>
            <div className="space-y-3">
              {[
                'Explore high-traffic location',
                'Conduct site visit analysis',
                'Review lease terms',
                'Schedule demographic study'
              ].map((action, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors cursor-pointer"
                >
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="text-sm text-gray-700">{action}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}