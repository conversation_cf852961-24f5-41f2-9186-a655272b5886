'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Brain,
  TrendingUp,
  Users,
  MapPin,
  BarChart3,
  Search,
  Filter,
  Download,
  Zap,
  Target,
  Globe,
  Clock,
  DollarSign,
  Star
} from 'lucide-react';

interface MarketInsight {
  id: string;
  title: string;
  category: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  description: string;
  data: any;
}

export default function InteractiveMarketResearch() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedInsight, setSelectedInsight] = useState<MarketInsight | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const mockInsights: MarketInsight[] = [
    {
      id: '1',
      title: 'Competitor Price Analysis',
      category: 'competitive',
      confidence: 92,
      impact: 'high',
      description: 'Your menu prices are 15% above market average for similar cuisine',
      data: { avgPrice: 24.5, yourPrice: 28.2, savings: 450 }
    },
    {
      id: '2',
      title: 'Peak Hours Optimization',
      category: 'operational',
      confidence: 87,
      impact: 'medium',
      description: 'Untapped revenue potential during 3-5 PM window',
      data: { potentialRevenue: 1200, currentUtilization: 35 }
    },
    {
      id: '3',
      title: 'Demographic Trends',
      category: 'customer',
      confidence: 94,
      impact: 'high',
      description: 'Growing demand for plant-based options in your area',
      data: { growth: 23, marketSize: 15000, opportunity: 3500 }
    }
  ];

  const tabs = [
    { id: 'overview', label: 'Market Overview', icon: Globe },
    { id: 'competitive', label: 'Competitive Analysis', icon: Target },
    { id: 'customer', label: 'Customer Insights', icon: Users },
    { id: 'trends', label: 'Market Trends', icon: TrendingUp }
  ];

  const handleAnalyze = () => {
    setIsAnalyzing(true);
    setTimeout(() => {
      setIsAnalyzing(false);
    }, 3000);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          Interactive Market Research
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          AI-powered insights to optimize your restaurant's market position
        </p>
        
        {/* Action Bar */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleAnalyze}
            disabled={isAnalyzing}
            className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all"
          >
            {isAnalyzing ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Zap className="h-5 w-5" />
              </motion.div>
            ) : (
              <Brain className="h-5 w-5" />
            )}
            <span>{isAnalyzing ? 'Analyzing...' : 'Run AI Analysis'}</span>
          </motion.button>
          
          <button className="flex items-center space-x-2 bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
            <Download className="h-5 w-5" />
            <span>Export Report</span>
          </button>
        </div>
      </motion.div>

      {/* Tab Navigation */}
      <div className="flex justify-center">
        <div className="bg-white rounded-xl border border-gray-200 p-2 shadow-sm">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Insights Grid */}
          <div className="grid md:grid-cols-2 gap-6">
            {mockInsights
              .filter(insight => activeTab === 'overview' || insight.category === activeTab)
              .map((insight, index) => (
                <motion.div
                  key={insight.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -5, scale: 1.02 }}
                  onClick={() => setSelectedInsight(insight)}
                  className="bg-white rounded-xl border border-gray-200 p-6 cursor-pointer hover:border-blue-500 transition-all shadow-sm hover:shadow-md"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                      insight.impact === 'high' ? 'bg-red-100 text-red-500' :
                      insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-500' :
                      'bg-green-100 text-green-500'
                    }`}>
                      <BarChart3 className="h-6 w-6" />
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">Confidence</div>
                      <div className="text-xl font-bold text-gray-800">{insight.confidence}%</div>
                    </div>
                  </div>
                  
                  <h3 className="text-lg font-bold text-gray-800 mb-2">
                    {insight.title}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    {insight.description}
                  </p>
                  
                  <div className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium ${
                    insight.impact === 'high' ? 'bg-red-100 text-red-700' :
                    insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    <Star className="h-3 w-3" />
                    <span>{insight.impact.toUpperCase()} IMPACT</span>
                  </div>
                </motion.div>
            ))}
          </div>

          {/* Interactive Analysis Section */}
          {isAnalyzing && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl p-8 text-white text-center"
            >
              <motion.div
                animate={{ 
                  scale: [1, 1.2, 1],
                  rotate: [0, 360]
                }}
                transition={{ 
                  duration: 2, 
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <Brain className="h-8 w-8" />
              </motion.div>
              <h3 className="text-2xl font-bold mb-2">AI Analysis in Progress</h3>
              <p className="text-blue-100">
                Analyzing market data, competitor intelligence, and customer behavior patterns...
              </p>
              <div className="mt-6 bg-white/20 rounded-full h-2">
                <motion.div
                  className="bg-white h-full rounded-full"
                  initial={{ width: '0%' }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 3 }}
                />
              </div>
            </motion.div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              Market Overview
            </h3>
            <div className="space-y-4">
              {[
                { label: 'Market Position', value: '#3', icon: Target, color: 'text-blue-500' },
                { label: 'Avg. Rating', value: '4.2', icon: Star, color: 'text-yellow-500' },
                { label: 'Price Range', value: '$$', icon: DollarSign, color: 'text-green-500' },
                { label: 'Peak Hours', value: '7-9 PM', icon: Clock, color: 'text-purple-500' }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center space-x-3">
                    <stat.icon className={`h-5 w-5 ${stat.color}`} />
                    <span className="text-gray-600">{stat.label}</span>
                  </div>
                  <span className="font-bold text-gray-800">{stat.value}</span>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Recent Analysis */}
          <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              Recent Analysis
            </h3>
            <div className="space-y-3">
              {[
                'Competitor menu analysis',
                'Peak hours optimization',
                'Customer demographic shift',
                'Pricing strategy review'
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                >
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <span className="text-sm text-gray-700">{item}</span>
                </motion.div>
              ))}
            </div>
          </div>

          {/* AI Recommendations */}
          <div className="bg-gradient-to-br from-orange-500 to-red-500 rounded-xl p-6 text-white">
            <div className="flex items-center space-x-2 mb-4">
              <Zap className="h-6 w-6" />
              <h3 className="text-lg font-bold">AI Recommendations</h3>
            </div>
            <div className="space-y-3">
              <div className="bg-white/20 rounded-lg p-3">
                <div className="text-sm font-medium mb-1">Menu Optimization</div>
                <div className="text-xs text-orange-100">
                  Add 2-3 plant-based options to capture growing market
                </div>
              </div>
              <div className="bg-white/20 rounded-lg p-3">
                <div className="text-sm font-medium mb-1">Pricing Strategy</div>
                <div className="text-xs text-orange-100">
                  Reduce appetizer prices by 10% to match competitors
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Insight Modal */}
      {selectedInsight && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedInsight(null)}
        >
          <motion.div
            initial={{ scale: 0.95 }}
            animate={{ scale: 1 }}
            className="bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[80vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-start justify-between mb-6">
              <div>
                <h3 className="text-2xl font-bold text-gray-800 mb-2">
                  {selectedInsight.title}
                </h3>
                <p className="text-gray-600">{selectedInsight.description}</p>
              </div>
              <button
                onClick={() => setSelectedInsight(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            {/* Detailed data visualization would go here */}
            <div className="bg-gray-50 rounded-xl p-6">
              <h4 className="font-semibold text-gray-800 mb-4">Key Metrics</h4>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(selectedInsight.data).map(([key, value]) => (
                  <div key={key} className="text-center">
                    <div className="text-2xl font-bold text-blue-500">{String(value)}</div>
                    <div className="text-sm text-gray-600 capitalize">{key}</div>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </div>
  );
}