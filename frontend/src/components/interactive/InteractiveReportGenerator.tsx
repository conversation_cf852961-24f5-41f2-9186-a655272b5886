'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  FileText,
  BarChart3,
  PieChart,
  TrendingUp,
  Download,
  Eye,
  Plus,
  Trash2,
  Edit,
  Filter,
  Calendar,
  Settings,
  Layout,
  Image,
  Table,
  Zap,
  Save,
  Share,
  Clock,
  Star
} from 'lucide-react';

interface ReportWidget {
  id: string;
  type: 'chart' | 'metric' | 'table' | 'text';
  title: string;
  data: any;
  position: { x: number; y: number };
  size: { width: number; height: number };
}

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  widgets: ReportWidget[];
  preview: string;
}

export default function InteractiveReportGenerator() {
  const [activeTab, setActiveTab] = useState('builder');
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [reportWidgets, setReportWidgets] = useState<ReportWidget[]>([]);
  const [draggedWidget, setDraggedWidget] = useState<string | null>(null);

  const widgetTypes = [
    { id: 'revenue-chart', name: 'Revenue Chart', icon: BarChart3, type: 'chart' },
    { id: 'customer-pie', name: 'Customer Breakdown', icon: PieChart, type: 'chart' },
    { id: 'trend-line', name: 'Trend Analysis', icon: TrendingUp, type: 'chart' },
    { id: 'kpi-metric', name: 'KPI Metric', icon: BarChart3, type: 'metric' },
    { id: 'data-table', name: 'Data Table', icon: Table, type: 'table' },
    { id: 'text-section', name: 'Text Section', icon: FileText, type: 'text' }
  ];

  const reportTemplates: ReportTemplate[] = [
    {
      id: '1',
      name: 'Monthly Performance',
      description: 'Comprehensive monthly business review',
      category: 'performance',
      widgets: [],
      preview: '/api/placeholder/300/200'
    },
    {
      id: '2',
      name: 'Customer Analytics',
      description: 'Deep dive into customer behavior and preferences',
      category: 'customer',
      widgets: [],
      preview: '/api/placeholder/300/200'
    },
    {
      id: '3',
      name: 'Financial Summary',
      description: 'Revenue, costs, and profitability analysis',
      category: 'financial',
      widgets: [],
      preview: '/api/placeholder/300/200'
    },
    {
      id: '4',
      name: 'Operational Insights',
      description: 'Staff efficiency and operational metrics',
      category: 'operational',
      widgets: [],
      preview: '/api/placeholder/300/200'
    }
  ];

  const tabs = [
    { id: 'builder', label: 'Report Builder', icon: Layout },
    { id: 'templates', label: 'Templates', icon: FileText },
    { id: 'preview', label: 'Preview', icon: Eye },
    { id: 'settings', label: 'Settings', icon: Settings }
  ];

  const addWidget = (widgetType: any) => {
    const newWidget: ReportWidget = {
      id: `widget-${Date.now()}`,
      type: widgetType.type,
      title: widgetType.name,
      data: {},
      position: { x: 0, y: reportWidgets.length * 150 },
      size: { width: 400, height: 300 }
    };
    setReportWidgets([...reportWidgets, newWidget]);
  };

  const removeWidget = (widgetId: string) => {
    setReportWidgets(widgets => widgets.filter(w => w.id !== widgetId));
  };

  const generateReport = () => {
    // Mock report generation
    console.log('Generating report with widgets:', reportWidgets);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h1 className="text-4xl font-bold text-gray-800 mb-4">
          Interactive Report Generator
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Create custom reports and business intelligence dashboards
        </p>
        
        {/* Action Bar */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={generateReport}
            className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all"
          >
            <Zap className="h-5 w-5" />
            <span>Generate Report</span>
          </motion.button>
          
          <button className="flex items-center space-x-2 bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
            <Save className="h-5 w-5" />
            <span>Save Template</span>
          </button>
          
          <button className="flex items-center space-x-2 bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
            <Share className="h-5 w-5" />
            <span>Share</span>
          </button>
        </div>
      </motion.div>

      {/* Tab Navigation */}
      <div className="flex justify-center">
        <div className="bg-white rounded-xl border border-gray-200 p-2 shadow-sm">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-purple-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="h-5 w-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div className="grid lg:grid-cols-4 gap-8">
        {/* Widget Library Sidebar */}
        {activeTab === 'builder' && (
          <div className="space-y-6">
            {/* Widget Types */}
            <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                <Plus className="h-5 w-5 mr-2" />
                Add Widgets
              </h3>
              <div className="space-y-2">
                {widgetTypes.map((widget, index) => (
                  <motion.button
                    key={widget.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, x: 5 }}
                    onClick={() => addWidget(widget)}
                    className="w-full flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-purple-50 hover:border-purple-200 border border-transparent transition-all text-left"
                  >
                    <widget.icon className="h-5 w-5 text-purple-500" />
                    <span className="text-sm font-medium text-gray-700">
                      {widget.name}
                    </span>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Report Settings */}
            <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Report Settings
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Report Title
                  </label>
                  <input
                    type="text"
                    placeholder="Enter report title..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date Range
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    <option>Last 30 days</option>
                    <option>Last 3 months</option>
                    <option>Last year</option>
                    <option>Custom range</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Export Format
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-sm">
                    <option>PDF</option>
                    <option>Excel</option>
                    <option>PowerPoint</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Templates Sidebar */}
        {activeTab === 'templates' && (
          <div className="space-y-6">
            <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-sm">
              <h3 className="text-lg font-bold text-gray-800 mb-4">
                Report Templates
              </h3>
              <div className="space-y-4">
                {reportTemplates.map((template, index) => (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    onClick={() => setSelectedTemplate(template)}
                    className="p-4 border border-gray-200 rounded-lg hover:border-purple-500 transition-all cursor-pointer"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <FileText className="h-5 w-5 text-purple-500" />
                      <span className="font-semibold text-gray-800">
                        {template.name}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">
                      {template.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">
                        {template.category}
                      </span>
                      <button className="text-sm text-purple-500 hover:text-purple-600 font-medium">
                        Use Template
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className={`${activeTab === 'builder' || activeTab === 'templates' ? 'lg:col-span-3' : 'lg:col-span-4'}`}>
          {activeTab === 'builder' && (
            <div className="bg-white rounded-2xl border border-gray-200 shadow-lg min-h-[600px]">
              {/* Canvas Header */}
              <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-4 text-white rounded-t-2xl">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-bold">Report Canvas</h3>
                  <div className="flex items-center space-x-2">
                    <button className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                      <Eye className="h-5 w-5" />
                    </button>
                    <button className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors">
                      <Download className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Canvas Area */}
              <div className="p-6 min-h-[500px] relative">
                {reportWidgets.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-center">
                    <div>
                      <Layout className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                      <h4 className="text-xl font-bold text-gray-400 mb-2">
                        Start Building Your Report
                      </h4>
                      <p className="text-gray-500">
                        Add widgets from the sidebar to create your custom report
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {reportWidgets.map((widget, index) => (
                      <motion.div
                        key={widget.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-xl p-6 hover:border-purple-500 transition-all group"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="font-semibold text-gray-800">
                            {widget.title}
                          </h4>
                          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <button className="p-1 text-gray-500 hover:text-purple-500">
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => removeWidget(widget.id)}
                              className="p-1 text-gray-500 hover:text-red-500"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        
                        {/* Widget Preview */}
                        <div className="h-32 bg-white rounded-lg border flex items-center justify-center">
                          <div className="text-center text-gray-400">
                            <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                            <span className="text-sm">{widget.type} widget</span>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'templates' && (
            <div className="grid md:grid-cols-2 gap-6">
              {reportTemplates.map((template, index) => (
                <motion.div
                  key={template.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ y: -5, scale: 1.02 }}
                  className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden cursor-pointer hover:border-purple-500 transition-all"
                >
                  {/* Template Preview */}
                  <div className="h-48 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                    <FileText className="h-16 w-16 text-purple-500" />
                  </div>
                  
                  {/* Template Info */}
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-xl font-bold text-gray-800">
                        {template.name}
                      </h3>
                      <Star className="h-5 w-5 text-yellow-500" />
                    </div>
                    <p className="text-gray-600 mb-4">
                      {template.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">
                        {template.category}
                      </span>
                      <button className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg font-semibold transition-colors">
                        Use Template
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {activeTab === 'preview' && (
            <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-8">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-2">
                  Report Preview
                </h3>
                <p className="text-gray-600">
                  This is how your report will look when generated
                </p>
              </div>
              
              {/* Mock Report Preview */}
              <div className="space-y-8">
                <div className="text-center">
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">
                    Monthly Performance Report
                  </h1>
                  <p className="text-gray-600">
                    January 2024 - Generated on {new Date().toLocaleDateString()}
                  </p>
                </div>
                
                <div className="grid md:grid-cols-3 gap-6">
                  {[
                    { title: 'Total Revenue', value: '$45,230', change: '+12%' },
                    { title: 'Orders', value: '1,247', change: '+8%' },
                    { title: 'Avg Order Value', value: '$36.28', change: '+4%' }
                  ].map((metric, index) => (
                    <div key={index} className="bg-gray-50 rounded-xl p-6 text-center">
                      <h4 className="text-lg font-semibold text-gray-800 mb-2">
                        {metric.title}
                      </h4>
                      <div className="text-3xl font-bold text-purple-500 mb-1">
                        {metric.value}
                      </div>
                      <div className="text-green-500 text-sm font-medium">
                        {metric.change}
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="h-64 bg-gray-50 rounded-xl flex items-center justify-center">
                  <div className="text-center text-gray-400">
                    <BarChart3 className="h-16 w-16 mx-auto mb-4" />
                    <p>Revenue Trend Chart</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}