"use client";

/**
 * Customer Onboarding & Support
 * Comprehensive onboarding flows, help documentation, support ticketing, and customer success tools
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, Circle, ArrowRight, ArrowLeft, Play, Book, 
  MessageCircle, HelpCircle, Star, Target, Users, Zap,
  MapPin, BarChart3, FileText, Settings, Lightbulb
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useTenant } from '@/hooks/useTenant';
import { apiClient } from '@/lib/api-client';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  completed: boolean;
  required: boolean;
  estimatedTime: string;
  component: React.ComponentType<any>;
}

interface OnboardingProgress {
  currentStep: number;
  completedSteps: string[];
  totalSteps: number;
  progressPercentage: number;
  timeSpent: number;
  lastActivity: string;
}

export function OnboardingFlow() {
  const { user } = useAuth();
  const { tenant } = useTenant();
  
  const [progress, setProgress] = useState<OnboardingProgress>({
    currentStep: 0,
    completedSteps: [],
    totalSteps: 7,
    progressPercentage: 0,
    timeSpent: 0,
    lastActivity: new Date().toISOString()
  });
  
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);

  // Onboarding steps configuration
  const onboardingSteps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to BiteBase Intelligence',
      description: 'Get started with your restaurant intelligence platform',
      icon: Star,
      completed: false,
      required: true,
      estimatedTime: '2 min',
      component: WelcomeStep
    },
    {
      id: 'company-setup',
      title: 'Company Information',
      description: 'Tell us about your restaurant business',
      icon: Users,
      completed: false,
      required: true,
      estimatedTime: '5 min',
      component: CompanySetupStep
    },
    {
      id: 'location-setup',
      title: 'Primary Location',
      description: 'Add your main restaurant location',
      icon: MapPin,
      completed: false,
      required: true,
      estimatedTime: '3 min',
      component: LocationSetupStep
    },
    {
      id: 'goals-setup',
      title: 'Business Goals',
      description: 'Define what you want to achieve',
      icon: Target,
      completed: false,
      required: true,
      estimatedTime: '4 min',
      component: GoalsSetupStep
    },
    {
      id: 'first-analysis',
      title: 'Your First Analysis',
      description: 'Run your first location analysis',
      icon: BarChart3,
      completed: false,
      required: true,
      estimatedTime: '5 min',
      component: FirstAnalysisStep
    },
    {
      id: 'team-setup',
      title: 'Invite Team Members',
      description: 'Add your team to collaborate',
      icon: Users,
      completed: false,
      required: false,
      estimatedTime: '3 min',
      component: TeamSetupStep
    },
    {
      id: 'completion',
      title: 'You\'re All Set!',
      description: 'Start exploring your restaurant intelligence',
      icon: CheckCircle,
      completed: false,
      required: true,
      estimatedTime: '1 min',
      component: CompletionStep
    }
  ];

  // Load onboarding progress
  useEffect(() => {
    loadOnboardingProgress();
  }, []);

  const loadOnboardingProgress = async () => {
    try {
      const progressData = await apiClient.onboarding.getProgress();
      setProgress(progressData);
      setActiveStep(progressData.currentStep);
    } catch (error) {
      console.error('Failed to load onboarding progress:', error);
    }
  };

  const updateProgress = async (stepId: string, completed: boolean = true) => {
    try {
      setLoading(true);
      
      const updatedProgress = await apiClient.onboarding.updateProgress({
        stepId,
        completed,
        timeSpent: progress.timeSpent + 1
      });
      
      setProgress(updatedProgress);
      
      if (completed && activeStep < onboardingSteps.length - 1) {
        setActiveStep(activeStep + 1);
      }
    } catch (error) {
      console.error('Failed to update progress:', error);
    } finally {
      setLoading(false);
    }
  };

  const goToStep = (stepIndex: number) => {
    if (stepIndex >= 0 && stepIndex < onboardingSteps.length) {
      setActiveStep(stepIndex);
    }
  };

  const currentStepData = onboardingSteps[activeStep];
  const CurrentStepComponent = currentStepData.component;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Getting Started</h1>
              <p className="text-gray-600">Let's set up your restaurant intelligence platform</p>
            </div>
            <Badge variant="outline">
              Step {activeStep + 1} of {onboardingSteps.length}
            </Badge>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <Progress value={progress.progressPercentage} className="h-2" />
            <div className="flex justify-between mt-2 text-sm text-gray-500">
              <span>{progress.completedSteps.length} steps completed</span>
              <span>{Math.round(progress.progressPercentage)}% complete</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Step Navigation */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Progress</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {onboardingSteps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex items-center space-x-3 p-2 rounded-lg cursor-pointer transition-colors ${
                      index === activeStep 
                        ? 'bg-blue-50 border border-blue-200' 
                        : progress.completedSteps.includes(step.id)
                        ? 'bg-green-50'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => goToStep(index)}
                  >
                    <div className="flex-shrink-0">
                      {progress.completedSteps.includes(step.id) ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : index === activeStep ? (
                        <Circle className="h-5 w-5 text-blue-600 fill-current" />
                      ) : (
                        <Circle className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className={`text-sm font-medium ${
                        index === activeStep ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {step.title}
                      </p>
                      <p className="text-xs text-gray-500">{step.estimatedTime}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Help Section */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <HelpCircle className="h-5 w-5 mr-2" />
                  Need Help?
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Book className="h-4 w-4 mr-2" />
                  Documentation
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Live Chat
                </Button>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Play className="h-4 w-4 mr-2" />
                  Video Tutorials
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <currentStepData.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">{currentStepData.title}</CardTitle>
                    <CardDescription>{currentStepData.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CurrentStepComponent
                  onComplete={() => updateProgress(currentStepData.id)}
                  onNext={() => goToStep(activeStep + 1)}
                  onPrevious={() => goToStep(activeStep - 1)}
                  loading={loading}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

// Individual Step Components
function WelcomeStep({ onComplete, onNext }: any) {
  return (
    <div className="space-y-6">
      <div className="text-center">
        <div className="mx-auto w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6">
          <Star className="h-12 w-12 text-white" />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-4">
          Welcome to BiteBase Intelligence!
        </h3>
        <p className="text-lg text-gray-600 mb-6">
          Your AI-powered restaurant intelligence platform that helps you make data-driven decisions 
          for location selection, market analysis, and business growth.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="text-center p-4 border rounded-lg">
          <MapPin className="h-8 w-8 text-blue-600 mx-auto mb-2" />
          <h4 className="font-semibold">Location Analysis</h4>
          <p className="text-sm text-gray-600">Analyze potential restaurant locations with AI</p>
        </div>
        <div className="text-center p-4 border rounded-lg">
          <BarChart3 className="h-8 w-8 text-green-600 mx-auto mb-2" />
          <h4 className="font-semibold">Market Research</h4>
          <p className="text-sm text-gray-600">Get comprehensive market insights and reports</p>
        </div>
        <div className="text-center p-4 border rounded-lg">
          <Zap className="h-8 w-8 text-purple-600 mx-auto mb-2" />
          <h4 className="font-semibold">AI-Powered Insights</h4>
          <p className="text-sm text-gray-600">Receive intelligent recommendations and predictions</p>
        </div>
      </div>

      <div className="flex justify-end">
        <Button onClick={() => { onComplete(); onNext(); }} className="px-8">
          Get Started
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}

function CompanySetupStep({ onComplete, onNext, onPrevious }: any) {
  const [formData, setFormData] = useState({
    companyName: '',
    industry: 'restaurant',
    businessType: 'single_location',
    description: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onComplete();
    onNext();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="companyName">Company Name *</Label>
          <Input
            id="companyName"
            value={formData.companyName}
            onChange={(e) => setFormData({...formData, companyName: e.target.value})}
            placeholder="Your restaurant name"
            required
          />
        </div>
        <div>
          <Label htmlFor="businessType">Business Type</Label>
          <select
            id="businessType"
            value={formData.businessType}
            onChange={(e) => setFormData({...formData, businessType: e.target.value})}
            className="w-full p-2 border rounded-md"
          >
            <option value="single_location">Single Location</option>
            <option value="multi_location">Multiple Locations</option>
            <option value="franchise">Franchise</option>
            <option value="chain">Restaurant Chain</option>
          </select>
        </div>
      </div>

      <div>
        <Label htmlFor="description">Business Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData({...formData, description: e.target.value})}
          placeholder="Tell us about your restaurant concept, cuisine type, target market..."
          rows={4}
        />
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        <Button type="submit">
          Continue
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </form>
  );
}

function LocationSetupStep({ onComplete, onNext, onPrevious }: any) {
  const [formData, setFormData] = useState({
    address: '',
    city: '',
    state: '',
    zipCode: '',
    latitude: '',
    longitude: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onComplete();
    onNext();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <Label htmlFor="address">Street Address *</Label>
        <Input
          id="address"
          value={formData.address}
          onChange={(e) => setFormData({...formData, address: e.target.value})}
          placeholder="123 Main Street"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <Label htmlFor="city">City *</Label>
          <Input
            id="city"
            value={formData.city}
            onChange={(e) => setFormData({...formData, city: e.target.value})}
            placeholder="San Francisco"
            required
          />
        </div>
        <div>
          <Label htmlFor="state">State *</Label>
          <Input
            id="state"
            value={formData.state}
            onChange={(e) => setFormData({...formData, state: e.target.value})}
            placeholder="CA"
            required
          />
        </div>
        <div>
          <Label htmlFor="zipCode">ZIP Code *</Label>
          <Input
            id="zipCode"
            value={formData.zipCode}
            onChange={(e) => setFormData({...formData, zipCode: e.target.value})}
            placeholder="94102"
            required
          />
        </div>
      </div>

      <div className="p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start space-x-3">
          <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900">Pro Tip</h4>
            <p className="text-sm text-blue-700">
              We'll use this location to provide personalized market insights and competitor analysis 
              specific to your area.
            </p>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        <Button type="submit">
          Continue
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </form>
  );
}

function GoalsSetupStep({ onComplete, onNext, onPrevious }: any) {
  const [selectedGoals, setSelectedGoals] = useState<string[]>([]);

  const goals = [
    { id: 'location_analysis', title: 'Location Analysis', description: 'Find the best locations for new restaurants' },
    { id: 'market_research', title: 'Market Research', description: 'Understand your target market and competition' },
    { id: 'performance_optimization', title: 'Performance Optimization', description: 'Improve existing location performance' },
    { id: 'expansion_planning', title: 'Expansion Planning', description: 'Plan strategic business expansion' },
    { id: 'competitive_intelligence', title: 'Competitive Intelligence', description: 'Monitor and analyze competitors' },
    { id: 'customer_insights', title: 'Customer Insights', description: 'Understand customer behavior and preferences' }
  ];

  const toggleGoal = (goalId: string) => {
    setSelectedGoals(prev => 
      prev.includes(goalId) 
        ? prev.filter(id => id !== goalId)
        : [...prev, goalId]
    );
  };

  const handleSubmit = () => {
    if (selectedGoals.length > 0) {
      onComplete();
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">What are your primary goals?</h3>
        <p className="text-gray-600">Select all that apply to help us personalize your experience.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {goals.map((goal) => (
          <div
            key={goal.id}
            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
              selectedGoals.includes(goal.id)
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => toggleGoal(goal.id)}
          >
            <div className="flex items-start space-x-3">
              <div className="mt-1">
                {selectedGoals.includes(goal.id) ? (
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                ) : (
                  <Circle className="h-5 w-5 text-gray-400" />
                )}
              </div>
              <div>
                <h4 className="font-medium">{goal.title}</h4>
                <p className="text-sm text-gray-600">{goal.description}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        <Button 
          onClick={handleSubmit}
          disabled={selectedGoals.length === 0}
        >
          Continue
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}

function FirstAnalysisStep({ onComplete, onNext, onPrevious }: any) {
  const [analysisRunning, setAnalysisRunning] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);

  const runAnalysis = async () => {
    setAnalysisRunning(true);
    
    // Simulate analysis
    setTimeout(() => {
      setAnalysisRunning(false);
      setAnalysisComplete(true);
    }, 3000);
  };

  const handleContinue = () => {
    onComplete();
    onNext();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Let's run your first analysis!</h3>
        <p className="text-gray-600">
          We'll analyze your primary location to show you the power of BiteBase Intelligence.
        </p>
      </div>

      {!analysisRunning && !analysisComplete && (
        <div className="text-center">
          <Button onClick={runAnalysis} size="lg" className="px-8">
            <BarChart3 className="h-5 w-5 mr-2" />
            Start Location Analysis
          </Button>
        </div>
      )}

      {analysisRunning && (
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600">Analyzing your location...</p>
          <p className="text-sm text-gray-500">This may take a few moments</p>
        </div>
      )}

      {analysisComplete && (
        <div className="space-y-4">
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h4 className="text-lg font-semibold text-green-900">Analysis Complete!</h4>
            <p className="text-gray-600">Your location analysis is ready to view.</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">8.5/10</div>
              <div className="text-sm text-gray-600">Location Score</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">High</div>
              <div className="text-sm text-gray-600">Foot Traffic</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">12</div>
              <div className="text-sm text-gray-600">Competitors</div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        {analysisComplete && (
          <Button onClick={handleContinue}>
            Continue
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        )}
      </div>
    </div>
  );
}

function TeamSetupStep({ onComplete, onNext, onPrevious }: any) {
  const [emails, setEmails] = useState(['']);

  const addEmailField = () => {
    setEmails([...emails, '']);
  };

  const updateEmail = (index: number, value: string) => {
    const newEmails = [...emails];
    newEmails[index] = value;
    setEmails(newEmails);
  };

  const handleSubmit = () => {
    onComplete();
    onNext();
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">Invite Your Team</h3>
        <p className="text-gray-600">
          Add team members to collaborate on restaurant intelligence. You can skip this step and add them later.
        </p>
      </div>

      <div className="space-y-4">
        {emails.map((email, index) => (
          <div key={index}>
            <Label htmlFor={`email-${index}`}>Team Member Email</Label>
            <Input
              id={`email-${index}`}
              type="email"
              value={email}
              onChange={(e) => updateEmail(index, e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>
        ))}
        
        <Button type="button" variant="outline" onClick={addEmailField}>
          Add Another Email
        </Button>
      </div>

      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>
        <div className="space-x-2">
          <Button type="button" variant="outline" onClick={handleSubmit}>
            Skip for Now
          </Button>
          <Button onClick={handleSubmit}>
            Send Invites
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}

function CompletionStep({ onComplete }: any) {
  return (
    <div className="text-center space-y-6">
      <div className="mx-auto w-24 h-24 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center">
        <CheckCircle className="h-12 w-12 text-white" />
      </div>
      
      <div>
        <h3 className="text-2xl font-bold text-gray-900 mb-4">
          Congratulations! You're all set up.
        </h3>
        <p className="text-lg text-gray-600 mb-6">
          Your BiteBase Intelligence platform is ready to help you make data-driven decisions 
          for your restaurant business.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Button variant="outline" className="p-6 h-auto">
          <div className="text-center">
            <FileText className="h-8 w-8 mx-auto mb-2" />
            <div className="font-semibold">View Your Analysis</div>
            <div className="text-sm text-gray-600">See your location analysis results</div>
          </div>
        </Button>
        <Button variant="outline" className="p-6 h-auto">
          <div className="text-center">
            <Settings className="h-8 w-8 mx-auto mb-2" />
            <div className="font-semibold">Explore Features</div>
            <div className="text-sm text-gray-600">Discover all platform capabilities</div>
          </div>
        </Button>
      </div>

      <Button onClick={onComplete} size="lg" className="px-8">
        Go to Dashboard
        <ArrowRight className="h-4 w-4 ml-2" />
      </Button>
    </div>
  );
}

export default OnboardingFlow;
