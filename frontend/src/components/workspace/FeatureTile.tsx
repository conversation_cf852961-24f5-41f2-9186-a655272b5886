'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { LucideIcon, Lock, ArrowRight, CheckCircle } from 'lucide-react';

interface Feature {
  id: string;
  title: string;
  description: string;
  icon: LucideIcon;
  href: string;
  color: string;
  bgColor: string;
  enabled: boolean;
  stats: string;
  features: string[];
}

interface FeatureTileProps {
  feature: Feature;
  isHovered: boolean;
  onHover: (id: string | null) => void;
}

export default function FeatureTile({ feature, isHovered, onHover }: FeatureTileProps) {
  const Icon = feature.icon;

  const tileContent = (
    <motion.div
      className={`relative h-full bg-white rounded-2xl border-2 transition-all duration-500 overflow-hidden group ${
        feature.enabled
          ? 'border-gray-200 hover:border-orange-500 cursor-pointer'
          : 'border-gray-200 cursor-not-allowed opacity-60'
      }`}
      whileHover={feature.enabled ? { y: -8, scale: 1.02 } : {}}
      onHoverStart={() => feature.enabled && onHover(feature.id)}
      onHoverEnd={() => onHover(null)}
    >
      {/* Background Gradient */}
      <motion.div
        className={`absolute top-0 right-0 w-40 h-40 bg-gradient-to-br ${feature.bgColor} opacity-0 rounded-full -translate-y-20 translate-x-20 transition-all duration-500 ${
          feature.enabled ? 'group-hover:opacity-100 group-hover:scale-150' : ''
        }`}
      />

      {/* Lock overlay for disabled features */}
      {!feature.enabled && (
        <div className="absolute inset-0 bg-gray-50/80 flex items-center justify-center z-10">
          <div className="text-center">
            <Lock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500 font-medium">Setup Required</p>
          </div>
        </div>
      )}

      <div className="p-8 h-full flex flex-col relative z-10">
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <motion.div
            className={`w-16 h-16 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center shadow-lg`}
            whileHover={feature.enabled ? { rotate: 360, scale: 1.1 } : {}}
            transition={{ duration: 0.6 }}
          >
            <Icon className="h-8 w-8 text-white" />
          </motion.div>

          {feature.enabled && (
            <motion.div
              className={`inline-block bg-gradient-to-r ${feature.color} text-white px-3 py-1 rounded-full text-sm font-medium`}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              {feature.stats}
            </motion.div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1">
          <h3 className="text-2xl font-bold text-gray-800 mb-3 group-hover:text-gray-900 transition-colors">
            {feature.title}
          </h3>
          <p className="text-gray-600 mb-6 leading-relaxed">
            {feature.description}
          </p>

          {/* Features List */}
          <ul className="space-y-2 mb-6">
            {feature.features.map((item, index) => (
              <motion.li
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
                className="flex items-center space-x-3 text-sm text-gray-700"
              >
                <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                <span>{item}</span>
              </motion.li>
            ))}
          </ul>
        </div>

        {/* CTA */}
        {feature.enabled && (
          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
            <span className="text-orange-500 font-semibold">
              Explore Interactive Features
            </span>
            <motion.div
              animate={{ x: isHovered ? 5 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ArrowRight
                className={`h-5 w-5 text-orange-500 transition-transform ${
                  isHovered ? 'translate-x-1' : ''
                }`}
              />
            </motion.div>
          </div>
        )}

        {/* Hover Effect Overlay */}
        {feature.enabled && (
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-2xl"
          />
        )}
      </div>
    </motion.div>
  );

  if (feature.enabled) {
    return <Link href={feature.href}>{tileContent}</Link>;
  }

  return tileContent;
}