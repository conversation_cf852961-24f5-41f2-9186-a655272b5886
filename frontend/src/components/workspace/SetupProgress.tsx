'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Check<PERSON>ir<PERSON>, Circle, ArrowRight, Settings } from 'lucide-react';

interface SetupStep {
  id: string;
  name: string;
  completed: boolean;
}

interface SetupProgress {
  completed: boolean;
  steps: SetupStep[];
}

interface SetupProgressProps {
  progress: SetupProgress;
}

export default function SetupProgress({ progress }: SetupProgressProps) {
  const completedSteps = progress.steps.filter(step => step.completed).length;
  const totalSteps = progress.steps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-2xl border border-orange-200 shadow-lg overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-500 to-red-500 p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-bold mb-2">Restaurant Setup</h3>
            <p className="text-orange-100">
              Complete your setup to unlock all interactive features
            </p>
          </div>
          <Settings className="h-8 w-8 text-orange-200" />
        </div>

        {/* Progress Bar */}
        <div className="mt-6">
          <div className="flex justify-between text-sm mb-2">
            <span>Setup Progress</span>
            <span>{completedSteps} of {totalSteps} completed</span>
          </div>
          <div className="w-full bg-orange-400/30 rounded-full h-3">
            <motion.div
              className="bg-white rounded-full h-3 shadow-sm"
              initial={{ width: 0 }}
              animate={{ width: `${progressPercentage}%` }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </div>
        </div>
      </div>

      {/* Steps */}
      <div className="p-6">
        <div className="space-y-4">
          {progress.steps.map((step, index) => (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 + index * 0.1 }}
              className={`flex items-center space-x-4 p-4 rounded-lg transition-colors ${
                step.completed 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-gray-50 border border-gray-200'
              }`}
            >
              {step.completed ? (
                <CheckCircle className="h-6 w-6 text-green-500 flex-shrink-0" />
              ) : (
                <Circle className="h-6 w-6 text-gray-400 flex-shrink-0" />
              )}
              
              <div className="flex-1">
                <h4 className={`font-semibold ${
                  step.completed ? 'text-green-800' : 'text-gray-700'
                }`}>
                  {step.name}
                </h4>
              </div>

              {step.completed && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  className="text-green-500 text-sm font-medium"
                >
                  Complete
                </motion.div>
              )}
            </motion.div>
          ))}
        </div>

        {/* Continue Setup Button */}
        {!progress.completed && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="mt-6 text-center"
          >
            <Link href="/onboarding/setup">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="inline-flex items-center space-x-2 bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold transition-colors shadow-lg"
              >
                <span>Continue Setup</span>
                <ArrowRight className="h-5 w-5" />
              </motion.button>
            </Link>
          </motion.div>
        )}

        {/* Success Message */}
        {progress.completed && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8 }}
            className="mt-6 p-4 bg-green-100 border border-green-200 rounded-lg text-center"
          >
            <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <h4 className="font-semibold text-green-800 mb-1">
              Setup Complete!
            </h4>
            <p className="text-green-600 text-sm">
              All interactive features are now available
            </p>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}