'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  BarChart3,
  MapPin,
  FileText,
  Settings,
  Brain,
  Target,
  TrendingUp,
  Globe,
  Users,
  Calendar,
  PieChart,
  Zap,
  Lock,
  CheckCircle
} from 'lucide-react';
import FeatureTile from './FeatureTile';
import SetupProgress from './SetupProgress';

interface WorkspaceHubProps {
  setupRequired?: boolean;
}

export default function WorkspaceHub({ setupRequired = false }: WorkspaceHubProps) {
  const { t } = useLanguage();
  const [hoveredTile, setHoveredTile] = useState<string | null>(null);

  // Mock setup progress - in real app, this would come from API
  const setupProgress = {
    completed: !setupRequired,
    steps: [
      { id: 'profile', name: 'Restaurant Profile', completed: true },
      { id: 'location', name: 'Location Setup', completed: true },
      { id: 'integrations', name: 'Data Integrations', completed: false },
      { id: 'preferences', name: 'Preferences', completed: false }
    ]
  };

  const interactiveFeatures = [
    {
      id: 'market-research',
      title: t('workspace.marketResearch'),
      description: 'AI-powered market analysis and competitive intelligence',
      icon: Brain,
      href: '/research-agent',
      color: 'from-blue-500 to-cyan-500',
      bgColor: 'from-blue-50 to-cyan-50',
      enabled: !setupRequired,
      stats: '15+ Data Sources',
      features: ['Competitor Analysis', 'Market Trends', 'Customer Insights']
    },
    {
      id: 'map-intelligence',
      title: t('workspace.mapIntelligence'),
      description: 'Location-based analytics and demographic insights',
      icon: MapPin,
      href: '/location-intelligence',
      color: 'from-green-500 to-emerald-500',
      bgColor: 'from-green-50 to-emerald-50',
      enabled: !setupRequired,
      stats: '50+ Countries',
      features: ['Site Selection', 'Demographics', 'Foot Traffic']
    },
    {
      id: 'report-generator',
      title: t('workspace.reportGenerator'),
      description: 'Create custom reports and business intelligence dashboards',
      icon: FileText,
      href: '/reports',
      color: 'from-purple-500 to-pink-500',
      bgColor: 'from-purple-50 to-pink-50',
      enabled: !setupRequired,
      stats: '20+ Templates',
      features: ['Custom Reports', 'Data Export', 'Scheduling']
    },
    {
      id: 'dashboard-overview',
      title: t('workspace.dashboardOverview'),
      description: 'Real-time business metrics and performance tracking',
      icon: BarChart3,
      href: '/dashboard',
      color: 'from-orange-500 to-red-500',
      bgColor: 'from-orange-50 to-red-50',
      enabled: !setupRequired,
      stats: 'Real-time Data',
      features: ['KPI Tracking', 'Performance Metrics', 'Alerts']
    }
  ];

  const quickActions = [
    {
      id: 'analytics',
      title: 'Analytics Hub',
      icon: TrendingUp,
      href: '/analytics',
      enabled: !setupRequired
    },
    {
      id: 'campaigns',
      title: 'Campaign Management',
      icon: Target,
      href: '/campaign-management',
      enabled: !setupRequired
    },
    {
      id: 'multi-location',
      title: 'Multi-Location',
      icon: Globe,
      href: '/multi-location',
      enabled: !setupRequired
    },
    {
      id: 'settings',
      title: 'Settings',
      icon: Settings,
      href: '/settings',
      enabled: true // Always enabled
    }
  ];

  return (
    <div className="space-y-8">
      {/* Setup Progress Section */}
      {setupRequired && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <SetupProgress progress={setupProgress} />
        </motion.div>
      )}

      {/* Main Interactive Features Grid */}
      <section>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Interactive Business Intelligence
          </h2>
          <p className="text-gray-600">
            Access powerful AI-driven tools to analyze and optimize your restaurant business
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-6 mb-12">
          {interactiveFeatures.map((feature, index) => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 + index * 0.1 }}
            >
              <FeatureTile
                feature={feature}
                isHovered={hoveredTile === feature.id}
                onHover={setHoveredTile}
              />
            </motion.div>
          ))}
        </div>
      </section>

      {/* Quick Actions Section */}
      <section>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mb-6"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Quick Actions
          </h2>
          <p className="text-gray-600">
            Fast access to essential restaurant management tools
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.9 + index * 0.1 }}
              whileHover={{ scale: 1.05, y: -5 }}
              whileTap={{ scale: 0.95 }}
            >
              {action.enabled ? (
                <Link href={action.href}>
                  <div className="bg-white rounded-xl p-6 border border-gray-200 hover:border-orange-500 transition-all duration-300 shadow-sm hover:shadow-md cursor-pointer group">
                    <action.icon className="h-8 w-8 text-orange-500 mb-3 group-hover:scale-110 transition-transform" />
                    <h3 className="font-semibold text-gray-800 text-sm">
                      {action.title}
                    </h3>
                  </div>
                </Link>
              ) : (
                <div className="bg-gray-50 rounded-xl p-6 border border-gray-200 cursor-not-allowed opacity-60">
                  <div className="relative">
                    <action.icon className="h-8 w-8 text-gray-400 mb-3" />
                    <Lock className="h-4 w-4 text-gray-400 absolute -top-1 -right-1" />
                  </div>
                  <h3 className="font-semibold text-gray-500 text-sm">
                    {action.title}
                  </h3>
                </div>
              )}
            </motion.div>
          ))}
        </div>
      </section>

      {/* Welcome Message for New Users */}
      {setupRequired && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl p-8 text-white text-center"
        >
          <Zap className="h-12 w-12 mx-auto mb-4" />
          <h3 className="text-2xl font-bold mb-2">
            Welcome to BiteBase Intelligence!
          </h3>
          <p className="text-orange-100 mb-6">
            Complete your restaurant setup to unlock all interactive features and start optimizing your business.
          </p>
          <Link href="/onboarding/setup">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-orange-500 px-8 py-3 rounded-lg font-semibold hover:bg-orange-50 transition-colors"
            >
              Complete Setup
            </motion.button>
          </Link>
        </motion.div>
      )}
    </div>
  );
}