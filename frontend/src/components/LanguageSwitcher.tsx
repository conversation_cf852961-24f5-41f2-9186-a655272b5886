'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

export function LanguageSwitcher() {
  const { language, setLanguage } = useLanguage();
  
  console.log('[LanguageSwitcher] Current language:', language);
  
  const languages = [
    { code: 'en' as const, name: 'English', flag: '🇺🇸' },
    { code: 'th' as const, name: 'ไทย', flag: '🇹🇭' },
  ];

  const currentLanguage = languages.find(lang => lang.code === language);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="gap-2">
          <Globe className="h-4 w-4" />
          <span className="hidden sm:inline">
            {currentLanguage?.flag} {currentLanguage?.name}
          </span>
          <span className="sm:hidden">
            {currentLanguage?.flag}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {languages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            onClick={() => {
              console.log('[LanguageSwitcher] Language change requested:', lang.code);
              setLanguage(lang.code);
              console.log('[LanguageSwitcher] Language change triggered');
            }}
            className={language === lang.code ? 'bg-accent' : ''}
          >
            <span className="mr-2">{lang.flag}</span>
            {lang.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Simple version for compact spaces
export function SimpleLanguageSwitcher() {
  const { language, setLanguage } = useLanguage();

  return (
    <div className="flex gap-1">
      <Button
        variant={language === 'en' ? 'default' : 'outline'}
        size="sm"
        onClick={() => setLanguage('en')}
        className="px-2 py-1 text-xs"
      >
        🇺🇸 EN
      </Button>
      <Button
        variant={language === 'th' ? 'default' : 'outline'}
        size="sm"
        onClick={() => setLanguage('th')}
        className="px-2 py-1 text-xs"
      >
        🇹🇭 TH
      </Button>
    </div>
  );
}
