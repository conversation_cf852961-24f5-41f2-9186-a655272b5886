'use client';

import React, { createContext, useContext, ReactNode } from 'react';

interface LanguageContextType {
  language: 'en';
  setLanguage: (lang: 'en') => void;
  t: (key: string, params?: Record<string, any>) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  // Simple English-only implementation
  const t = (key: string, params?: Record<string, any>): string => {
    // Return the key as is for now - this is a simplified implementation
    let result = key;
    
    // Handle basic interpolation
    if (params && typeof result === 'string') {
      Object.keys(params).forEach(param => {
        result = result.replace(new RegExp(`{${param}}`, 'g'), params[param]);
      });
    }
    
    return result;
  };

  const contextValue: LanguageContextType = {
    language: 'en',
    setLanguage: () => {}, // No-op for simplified version
    t
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    // Return a default context during SSR
    if (typeof window === 'undefined') {
      return {
        language: 'en',
        setLanguage: () => {},
        t: (key: string) => key,
      };
    }
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}