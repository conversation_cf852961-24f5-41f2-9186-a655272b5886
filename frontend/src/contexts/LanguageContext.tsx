'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'th';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, any>) => string;
}

// Create context with explicit type
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Enhanced translations for BiteBase Intelligence
const translations = {
  en: {
    common: {
      dashboard: 'Dashboard',
      analytics: 'Analytics',
      insights: 'Insights',
      settings: 'Settings',
      logout: 'Logout',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      create: 'Create',
      search: 'Search',
      filter: 'Filter',
      export: 'Export',
      import: 'Import',
      help: 'Help',
      profile: 'Profile',
      billing: 'Billing'
    },
    nav: {
      dashboard: 'Dashboard',
      locationIntelligence: 'Location Intelligence',
      researchAgent: 'AI Research Agent',
      analytics: 'Analytics',
      dataSources: 'Data Sources',
      reports: 'Reports',
      settings: 'Settings',
      help: 'Help'
    },
    landing: {
      title: 'AI-Powered Restaurant Intelligence',
      subtitle: 'Transform your restaurant business with data-driven insights, location intelligence, and predictive analytics',
      getStarted: 'Get started with our comprehensive business intelligence platform designed specifically for the restaurant industry'
    },
    dashboard: {
      title: 'Business Intelligence Dashboard',
      welcome: 'Welcome to your restaurant intelligence platform'
    },
    workspace: {
      title: 'Interactive Workspace',
      subtitle: 'Access powerful tools and insights for your restaurant business',
      marketResearch: 'Interactive Market Research',
      mapIntelligence: 'Interactive Map Intelligence',
      reportGenerator: 'Interactive Report Generator',
      dashboardOverview: 'Dashboard Overview',
      setupRequired: 'Complete restaurant setup to unlock this feature'
    }
  },
  th: {
    common: {
      dashboard: 'แดชบอร์ด',
      analytics: 'การวิเคราะห์',
      insights: 'ข้อมูลเชิงลึก',
      settings: 'การตั้งค่า',
      logout: 'ออกจากระบบ',
      loading: 'กำลังโหลด...',
      error: 'ข้อผิดพลาด',
      success: 'สำเร็จ',
      save: 'บันทึก',
      cancel: 'ยกเลิก',
      delete: 'ลบ',
      edit: 'แก้ไข',
      create: 'สร้าง',
      search: 'ค้นหา',
      filter: 'กรอง',
      export: 'ส่งออก',
      import: 'นำเข้า',
      help: 'ช่วยเหลือ',
      profile: 'โปรไฟล์',
      billing: 'การเรียกเก็บเงิน'
    },
    nav: {
      dashboard: 'แดชบอร์ด',
      locationIntelligence: 'ข่าวกรองตำแหน่ง',
      researchAgent: 'ผู้ช่วยวิจัย AI',
      analytics: 'การวิเคราะห์',
      dataSources: 'แหล่งข้อมูล',
      reports: 'รายงาน',
      settings: 'การตั้งค่า',
      help: 'ช่วยเหลือ'
    },
    landing: {
      title: 'ฺBiteBase Intelligence',
      subtitle: 'เปลี่ยนธุรกิจร้านอาหารของคุณด้วยข้อมูลเชิงลึก ด้วยทำเลที่ตั้งและการคาดการณ์',
      getStarted: 'เริ่มต้นกับแพลตฟอร์มปัญญาทางธุรกิจที่ออกแบบมาเฉพาะสำหรับอุตสาหกรรมร้านอาหาร'
    },
    dashboard: {
      title: 'แดชบอร์ดปัญญาทางธุรกิจ',
      welcome: 'ยินดีต้อนรับสู่แพลตฟอร์มปัญญาร้านอาหารของคุณ'
    },
    workspace: {
      title: 'พื้นที่ทำงานแบบโต้ตอบ',
      subtitle: 'เข้าถึงเครื่องมือและข้อมูลเชิงลึกที่ทรงพลังสำหรับธุรกิจร้านอาหารของคุณ',
      marketResearch: 'การวิจัยตลาดแบบโต้ตอบ',
      mapIntelligence: 'ข่าวกรองแผนที่แบบโต้ตอบ',
      reportGenerator: 'เครื่องมือสร้างรายงานแบบโต้ตอบ',
      dashboardOverview: 'ภาพรวมแดชบอร์ด',
      setupRequired: 'กรุณาตั้งค่าร้านอาหารให้เสร็จสิ้นเพื่อปลดล็อคฟีเจอร์นี้'
    }
  }
};

interface LanguageProviderProps {
  children: ReactNode;
}

// Provider component with error boundary
function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>('en');
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    try {
      if (typeof window !== 'undefined') {
        const savedLanguage = localStorage.getItem('language') as Language;
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'th')) {
          setLanguage(savedLanguage);
        }
      }
    } catch (error) {
      console.warn('Failed to load language from localStorage:', error);
    } finally {
      setIsInitialized(true);
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    try {
      setLanguage(lang);
      if (typeof window !== 'undefined') {
        localStorage.setItem('language', lang);
      }
    } catch (error) {
      console.warn('Failed to save language to localStorage:', error);
    }
  };

  const t = (key: string, params?: Record<string, any>): string => {
    try {
      const keys = key.split('.');
      let value: any = translations[language];
      
      for (const k of keys) {
        if (value && typeof value === 'object' && k in value) {
          value = value[k];
        } else {
          return key; // Return the key if translation not found
        }
      }
      
      let result = typeof value === 'string' ? value : key;
      
      // Handle interpolation
      if (params && typeof result === 'string') {
        Object.keys(params).forEach(param => {
          result = result.replace(new RegExp(`{${param}}`, 'g'), params[param]);
        });
      }
      
      return result;
    } catch (error) {
      console.warn('Translation error for key:', key, error);
      return key;
    }
  };

  // Don't render until initialized to prevent hydration issues
  if (!isInitialized) {
    return <div>Loading...</div>;
  }

  const contextValue: LanguageContextType = {
    language,
    setLanguage: handleSetLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
}

// Hook to use the language context with proper error handling
function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    // During SSR, return a default context to prevent errors
    if (typeof window === 'undefined') {
      return {
        language: 'en' as Language,
        setLanguage: () => {},
        t: (key: string) => key,
      };
    }
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Export with proper default export to fix module resolution issues
export default LanguageProvider;
export { useLanguage };
export type { Language, LanguageContextType };