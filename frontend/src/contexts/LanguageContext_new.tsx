'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';


interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, any>) => string;
}

// Create context with explicit type
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

import { loadTranslation } from '../locales';
import type { Language, TranslationSchema, TranslationKey } from '../locales/types';


interface LanguageProviderProps {
  children: ReactNode;
}

// Provider component
function LanguageProvider({ children }: LanguageProviderProps): React.ReactElement {
  const [language, setLanguage] = useState<Language>('en');
  const [translations, setTranslations] = useState<Partial<TranslationSchema>>({});
  const [loadedNamespaces, setLoadedNamespaces] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('language') as Language;
      console.log('[LanguageContext] Initial load - saved language:', savedLanguage);
      if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'th')) {
        setLanguage(savedLanguage);
        console.log('[LanguageContext] Language set to:', savedLanguage);
      } else {
        console.log('[LanguageContext] Using default language: en');
      }
    }
  }, []);

  useEffect(() => {
    // Preload critical namespaces
    loadNamespace('common');
    loadNamespace('navigation');
    loadNamespace('auth');
    loadNamespace('landing');
  }, [language]);

  const handleSetLanguage = (lang: Language) => {
    console.log('[LanguageContext] handleSetLanguage called with:', lang);
    setLanguage(lang);
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', lang);
      console.log('[LanguageContext] Language saved to localStorage:', lang);
    }
    setTranslations({});
    setLoadedNamespaces(new Set());
    console.log('[LanguageContext] Translations and namespaces cleared for reload');
  };

  const loadNamespace = async (namespace: TranslationKey) => {
    const key = `${language}.${namespace}`;
    if (loadedNamespaces.has(key)) {
      console.log('[LanguageContext] Namespace already loaded:', key);
      return;
    }
    console.log('[LanguageContext] Loading namespace:', key);
    try {
      const translation = await loadTranslation(language, namespace);
      setTranslations(prev => ({
        ...prev,
        [namespace]: translation
      }));
      setLoadedNamespaces(prev => new Set(prev).add(key));
      console.log('[LanguageContext] Namespace loaded successfully:', key);
    } catch (error) {
      console.error('[LanguageContext] Error loading namespace:', key, error);
      // Fallback handled in loadTranslation
    }
  };

  const t = (key: string, params?: Record<string, any>): string => {
    const keys = key.split('.');
    const namespace = keys[0] as TranslationKey;
    const nsKey = `${language}.${namespace}`;
    // Auto-load namespace if not loaded
    if (!loadedNamespaces.has(nsKey)) {
      loadNamespace(namespace);
      return key; // Return key while loading
    }
    let value: any = translations[namespace];
    for (let i = 1; i < keys.length; i++) {
      if (value && typeof value === 'object' && keys[i] in value) {
        value = value[keys[i]];
      } else {
        // Fallback to English if missing
        if (language !== 'en' && translations[namespace]) {
          let fallback: any = translations[namespace];
          for (let j = 1; j < keys.length; j++) {
            if (fallback && typeof fallback === 'object' && keys[j] in fallback) {
              fallback = fallback[keys[j]];
            } else {
              return key;
            }
          }
          return typeof fallback === 'string' ? fallback : key;
        }
        return key;
      }
    }
    let result = typeof value === 'string' ? value : key;
    // Handle interpolation
    if (params && typeof result === 'string') {
      Object.keys(params).forEach(param => {
        result = result.replace(new RegExp(`{${param}}`, 'g'), params[param]);
      });
    }
    return result;
  };

  const contextValue: LanguageContextType = {
    language,
    setLanguage: handleSetLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
}

// Hook to use the language context
function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    // During SSR, return a default context to prevent errors
    if (typeof window === 'undefined') {
      return {
        language: 'en' as Language,
        setLanguage: () => {},
        t: (key: string) => key,
      };
    }
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Export the provider and hook
export { LanguageProvider, useLanguage };
export type { Language, LanguageContextType };
