"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

// Development mode - disable auth
const DEVELOPMENT_MODE = true; // Always true for simplified development

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  company?: string;
  role?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, firstName: string, lastName: string, company?: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock user for development
const DEV_USER: User = {
  id: "dev-user-1",
  email: "<EMAIL>",
  firstName: "Developer",
  lastName: "User",
  company: "BiteBase Intelligence",
  role: "admin"
};

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    // During SSR, return a default context to prevent errors
    if (typeof window === 'undefined') {
      return {
        user: DEVELOPMENT_MODE ? DEV_USER : null,
        loading: false,
        signIn: async () => {},
        signUp: async () => {},
        logout: async () => {},
      };
    }
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(DEVELOPMENT_MODE ? DEV_USER : null);
  const [loading, setLoading] = useState(!DEVELOPMENT_MODE);

  // In development mode, automatically set user as authenticated
  useEffect(() => {
    if (DEVELOPMENT_MODE) {
      setUser(DEV_USER);
      setLoading(false);
    } else {
      // Production auth logic would go here
      const checkAuth = async () => {
        try {
          // Check for existing session
          const token = localStorage.getItem('auth_token');
          if (token) {
            // Validate token with backend
            // For now, just set loading to false
            setLoading(false);
          } else {
            setLoading(false);
          }
        } catch (error) {
          console.error("Auth check failed:", error);
          setLoading(false);
        }
      };
      checkAuth();
    }
  }, []);

  const signIn = async (email: string, password: string) => {
    if (DEVELOPMENT_MODE) {
      // In development, any email/password combo works
      setUser(DEV_USER);
      return;
    }
    
    setLoading(true);
    try {
      // Production login logic would go here
      console.log("Production login not implemented");
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string, firstName: string, lastName: string, company?: string) => {
    if (DEVELOPMENT_MODE) {
      // In development, any signup works
      setUser({
        ...DEV_USER,
        email,
        firstName,
        lastName,
        company
      });
      return;
    }

    setLoading(true);
    try {
      // Production signup logic would go here
      console.log("Production signup not implemented");
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    if (DEVELOPMENT_MODE) {
      // In development, just clear user but keep them "logged in" for easier development
      console.log("Development logout - staying logged in for easier development");
      return;
    }

    setLoading(true);
    try {
      setUser(null);
      localStorage.removeItem('auth_token');
    } catch (error) {
      console.error("Logout error:", error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    signIn,
    signUp,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
