'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type Language = 'en' | 'th';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, any>) => string;
}

// Create context with explicit type
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Enhanced translations for BiteBase Intelligence
const translations = {
  en: {
    common: {
      dashboard: 'Dashboard',
      analytics: 'Analytics',
      insights: 'Insights',
      settings: 'Settings',
      logout: 'Logout',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      save: 'Save',
      cancel: 'Cancel',
      delete: 'Delete',
      edit: 'Edit',
      create: 'Create',
      search: 'Search',
      filter: 'Filter',
      export: 'Export',
      import: 'Import',
      help: 'Help',
      profile: 'Profile',
      billing: 'Billing'
    },
    nav: {
      dashboard: 'Dashboard',
      locationIntelligence: 'Location Intelligence',
      researchAgent: 'AI Research Agent',
      analytics: 'Analytics',
      dataSources: 'Data Sources',
      reports: 'Reports',
      settings: 'Settings',
      help: 'Help'
    }
  },
  th: {
    common: {
      dashboard: 'แดชบอร์ด',
      analytics: 'การวิเคราะห์',
      insights: 'ข้อมูลเชิงลึก',
      settings: 'การตั้งค่า',
      logout: 'ออกจากระบบ',
      loading: 'กำลังโหลด...',
      error: 'ข้อผิดพลาด',
      success: 'สำเร็จ',
      save: 'บันทึก',
      cancel: 'ยกเลิก',
      delete: 'ลบ',
      edit: 'แก้ไข',
      create: 'สร้าง',
      search: 'ค้นหา',
      filter: 'กรอง',
      export: 'ส่งออก',
      import: 'นำเข้า',
      help: 'ช่วยเหลือ',
      profile: 'โปรไฟล์',
      billing: 'การเรียกเก็บเงิน'
    },
    nav: {
      dashboard: 'แดชบอร์ด',
      locationIntelligence: 'ข่าวกรองตำแหน่ง',
      researchAgent: 'เอเจนต์วิจัย AI',
      analytics: 'การวิเคราะห์',
      dataSources: 'แหล่งข้อมูล',
      reports: 'รายงาน',
      settings: 'การตั้งค่า',
      help: 'ช่วยเหลือ'
    }
  }
};

interface LanguageProviderProps {
  children: ReactNode;
}

// Provider component
function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>('en');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('language') as Language;
      if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'th')) {
        setLanguage(savedLanguage);
      }
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', lang);
    }
  };

  const t = (key: string, params?: Record<string, any>): string => {
    const keys = key.split('.');
    let value: any = translations[language];
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return the key if translation not found
      }
    }
    
    let result = typeof value === 'string' ? value : key;
    
    // Handle interpolation
    if (params && typeof result === 'string') {
      Object.keys(params).forEach(param => {
        result = result.replace(new RegExp(`{${param}}`, 'g'), params[param]);
      });
    }
    
    return result;
  };

  const contextValue: LanguageContextType = {
    language,
    setLanguage: handleSetLanguage,
    t
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
}

// Hook to use the language context
function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    // During SSR, return a default context to prevent errors
    if (typeof window === 'undefined') {
      return {
        language: 'en' as Language,
        setLanguage: () => {},
        t: (key: string) => key,
      };
    }
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Export the provider and hook
export { LanguageProvider, useLanguage };
export type { Language, LanguageContextType };

// Default export for the provider
export default LanguageProvider;
