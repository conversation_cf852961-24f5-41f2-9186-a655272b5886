#!/usr/bin/env node
const fs = require('fs');
const path = require('path');

// Test results object
const testResults = {
  summary: {
    totalTests: 0,
    passed: 0,
    failed: 0,
    warnings: 0
  },
  tests: [],
  issues: []
};

// Helper to log test results
function logTest(category, test, passed, details = '') {
  testResults.totalTests++;
  if (passed) {
    testResults.summary.passed++;
    console.log(`✓ ${category}: ${test}`);
  } else {
    testResults.summary.failed++;
    console.log(`✗ ${category}: ${test} ${details ? `- ${details}` : ''}`);
    testResults.issues.push({ category, test, details });
  }
  testResults.tests.push({ category, test, passed, details });
}

// Test 1: Check if all translation files exist
function testTranslationFiles() {
  console.log('\n=== Testing Translation Files ===');
  
  const localesDir = path.join(__dirname, 'src/locales');
  const languages = ['en', 'th'];
  const namespaces = [
    'common', 'navigation', 'auth', 'landing', 'dashboard',
    'analytics', 'restaurant', 'campaign', 'reports', 'settings'
  ];
  
  languages.forEach(lang => {
    namespaces.forEach(ns => {
      const filePath = path.join(localesDir, lang, `${ns}.ts`);
      const exists = fs.existsSync(filePath);
      logTest('Translation Files', `${lang}/${ns}.ts exists`, exists);
      
      if (exists) {
        // Check if file has content
        const content = fs.readFileSync(filePath, 'utf8');
        const hasExport = content.includes('export const') || content.includes('export default');
        logTest('Translation Files', `${lang}/${ns}.ts has exports`, hasExport);
      }
    });
  });
}

// Test 2: Check translation consistency
function testTranslationConsistency() {
  console.log('\n=== Testing Translation Consistency ===');
  
  const enDir = path.join(__dirname, 'src/locales/en');
  const thDir = path.join(__dirname, 'src/locales/th');
  
  const namespaces = fs.readdirSync(enDir).filter(f => f.endsWith('.ts'));
  
  namespaces.forEach(file => {
    const enPath = path.join(enDir, file);
    const thPath = path.join(thDir, file);
    
    if (fs.existsSync(thPath)) {
      const enContent = fs.readFileSync(enPath, 'utf8');
      const thContent = fs.readFileSync(thPath, 'utf8');
      
      // Extract keys using regex
      const enKeys = [...enContent.matchAll(/(\w+):\s*['"]/g)].map(m => m[1]);
      const thKeys = [...thContent.matchAll(/(\w+):\s*['"]/g)].map(m => m[1]);
      
      const missingInThai = enKeys.filter(k => !thKeys.includes(k));
      const extraInThai = thKeys.filter(k => !enKeys.includes(k));
      
      logTest(
        'Key Consistency',
        `${file} - same keys in both languages`,
        missingInThai.length === 0 && extraInThai.length === 0,
        missingInThai.length > 0 ? `Missing in Thai: ${missingInThai.join(', ')}` : 
        extraInThai.length > 0 ? `Extra in Thai: ${extraInThai.join(', ')}` : ''
      );
    }
  });
}

// Test 3: Check LanguageContext implementation
function testLanguageContext() {
  console.log('\n=== Testing Language Context ===');
  
  const contextPath = path.join(__dirname, 'src/contexts/LanguageContext.tsx');
  const contextNewPath = path.join(__dirname, 'src/contexts/LanguageContext_new.tsx');
  
  // Check which context file exists
  const contextExists = fs.existsSync(contextPath);
  const newContextExists = fs.existsSync(contextNewPath);
  
  logTest('Language Context', 'Context file exists', contextExists || newContextExists);
  
  if (contextExists || newContextExists) {
    const actualPath = contextExists ? contextPath : contextNewPath;
    const content = fs.readFileSync(actualPath, 'utf8');
    
    // Check for required functionality
    const hasProvider = content.includes('LanguageProvider');
    const hasUseLanguage = content.includes('useLanguage');
    const hasLocalStorage = content.includes('localStorage');
    const hasSetLanguage = content.includes('setLanguage');
    
    logTest('Language Context', 'Has LanguageProvider', hasProvider);
    logTest('Language Context', 'Has useLanguage hook', hasUseLanguage);
    logTest('Language Context', 'Uses localStorage', hasLocalStorage);
    logTest('Language Context', 'Has setLanguage function', hasSetLanguage);
  }
}

// Test 4: Check LanguageSwitcher component
function testLanguageSwitcher() {
  console.log('\n=== Testing Language Switcher Component ===');
  
  const switcherPath = path.join(__dirname, 'src/components/LanguageSwitcher.tsx');
  
  if (fs.existsSync(switcherPath)) {
    const content = fs.readFileSync(switcherPath, 'utf8');
    
    logTest('Language Switcher', 'Component file exists', true);
    logTest('Language Switcher', 'Uses useLanguage hook', content.includes('useLanguage'));
    logTest('Language Switcher', 'Has English option', content.includes('English') || content.includes('en'));
    logTest('Language Switcher', 'Has Thai option', content.includes('ไทย') || content.includes('th'));
    logTest('Language Switcher', 'Has click handlers', content.includes('onClick'));
  } else {
    logTest('Language Switcher', 'Component file exists', false);
  }
}

// Test 5: Check page implementations
function testPageImplementations() {
  console.log('\n=== Testing Page Implementations ===');
  
  const pages = [
    { path: 'src/app/page.tsx', name: 'Landing Page' },
    { path: 'src/app/auth/page.tsx', name: 'Auth Page' },
    { path: 'src/app/dashboard/page.tsx', name: 'Dashboard Page' }
  ];
  
  pages.forEach(page => {
    const fullPath = path.join(__dirname, page.path);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      logTest('Page Implementation', `${page.name} exists`, true);
      
      // Check if uses translation
      const usesTranslation = content.includes('useTranslation') || 
                             content.includes('useLanguage') ||
                             content.includes('t(');
      
      logTest('Page Implementation', `${page.name} uses translations`, usesTranslation);
    } else {
      logTest('Page Implementation', `${page.name} exists`, false);
    }
  });
}

// Test 6: Check layout integration
function testLayoutIntegration() {
  console.log('\n=== Testing Layout Integration ===');
  
  const layoutPath = path.join(__dirname, 'src/app/layout.tsx');
  const mainLayoutPath = path.join(__dirname, 'src/components/layout/MainLayout.tsx');
  
  if (fs.existsSync(layoutPath)) {
    const content = fs.readFileSync(layoutPath, 'utf8');
    const hasLanguageProvider = content.includes('LanguageProvider');
    logTest('Layout Integration', 'Root layout wraps with LanguageProvider', hasLanguageProvider);
  }
  
  if (fs.existsSync(mainLayoutPath)) {
    const content = fs.readFileSync(mainLayoutPath, 'utf8');
    const hasLanguageSwitcher = content.includes('LanguageSwitcher');
    logTest('Layout Integration', 'MainLayout includes LanguageSwitcher', hasLanguageSwitcher);
  }
}

// Generate test report
function generateReport() {
  console.log('\n=== TEST SUMMARY ===');
  console.log(`Total Tests: ${testResults.summary.totalTests}`);
  console.log(`Passed: ${testResults.summary.passed} (${Math.round(testResults.summary.passed / testResults.summary.totalTests * 100)}%)`);
  console.log(`Failed: ${testResults.summary.failed}`);
  
  if (testResults.issues.length > 0) {
    console.log('\n=== ISSUES FOUND ===');
    testResults.issues.forEach((issue, i) => {
      console.log(`${i + 1}. ${issue.category} - ${issue.test}: ${issue.details}`);
    });
  }
  
  // Save detailed report
  const reportPath = path.join(__dirname, 'language-test-results.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  console.log(`\nDetailed report saved to: ${reportPath}`);
  
  return testResults.summary.failed === 0;
}

// Run all tests
function runAllTests() {
  console.log('Starting Automated Language Testing...\n');
  
  testTranslationFiles();
  testTranslationConsistency();
  testLanguageContext();
  testLanguageSwitcher();
  testPageImplementations();
  testLayoutIntegration();
  
  const allPassed = generateReport();
  
  console.log('\n' + (allPassed ? '✅ All tests passed!' : '❌ Some tests failed. Please review the issues above.'));
  
  // Exit with appropriate code
  process.exit(allPassed ? 0 : 1);
}

// Run tests
runAllTests();