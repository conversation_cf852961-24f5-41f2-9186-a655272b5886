{"summary": {"totalTests": 0, "passed": 67, "failed": 1, "warnings": 0}, "tests": [{"category": "Translation Files", "test": "en/common.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/common.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/navigation.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/navigation.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/auth.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/auth.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/landing.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/landing.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/dashboard.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/dashboard.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/analytics.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/analytics.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/restaurant.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/restaurant.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/campaign.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/campaign.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/reports.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/reports.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/settings.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "en/settings.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/common.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/common.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/navigation.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/navigation.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/auth.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/auth.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/landing.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/landing.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/dashboard.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/dashboard.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/analytics.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/analytics.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/restaurant.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/restaurant.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/campaign.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/campaign.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/reports.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/reports.ts has exports", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/settings.ts exists", "passed": true, "details": ""}, {"category": "Translation Files", "test": "th/settings.ts has exports", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "analytics.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "auth.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "campaign.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "common.ts - same keys in both languages", "passed": false, "details": "Missing in Thai: included"}, {"category": "Key Consistency", "test": "dashboard.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "landing.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "navigation.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "reports.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "restaurant.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Key Consistency", "test": "settings.ts - same keys in both languages", "passed": true, "details": ""}, {"category": "Language Context", "test": "Context file exists", "passed": true, "details": ""}, {"category": "Language Context", "test": "<PERSON>", "passed": true, "details": ""}, {"category": "Language Context", "test": "Has useLanguage hook", "passed": true, "details": ""}, {"category": "Language Context", "test": "Uses localStorage", "passed": true, "details": ""}, {"category": "Language Context", "test": "Has setLanguage function", "passed": true, "details": ""}, {"category": "Language Switcher", "test": "Component file exists", "passed": true, "details": ""}, {"category": "Language Switcher", "test": "Uses useLanguage hook", "passed": true, "details": ""}, {"category": "Language Switcher", "test": "Has English option", "passed": true, "details": ""}, {"category": "Language Switcher", "test": "Has Thai option", "passed": true, "details": ""}, {"category": "Language Switcher", "test": "Has click handlers", "passed": true, "details": ""}, {"category": "Page Implementation", "test": "Landing Page exists", "passed": true, "details": ""}, {"category": "Page Implementation", "test": "Landing Page uses translations", "passed": true, "details": ""}, {"category": "Page Implementation", "test": "Auth Page exists", "passed": true, "details": ""}, {"category": "Page Implementation", "test": "Auth Page uses translations", "passed": true, "details": ""}, {"category": "Page Implementation", "test": "Dashboard Page exists", "passed": true, "details": ""}, {"category": "Page Implementation", "test": "Dashboard Page uses translations", "passed": true, "details": ""}, {"category": "Layout Integration", "test": "Root layout wraps with LanguageProvider", "passed": true, "details": ""}, {"category": "Layout Integration", "test": "MainLayout includes LanguageSwitcher", "passed": true, "details": ""}], "issues": [{"category": "Key Consistency", "test": "common.ts - same keys in both languages", "details": "Missing in Thai: included"}], "totalTests": null}