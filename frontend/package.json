{"name": "bitebase-intelligence-frontend", "version": "2.1.0", "private": true, "description": "BiteBase Intelligence - Enterprise Restaurant Intelligence Platform", "homepage": "https://intelligence.bitebase.app", "scripts": {"dev": "next dev -p 3000 -H 0.0.0.0", "dev:turbo": "next dev --turbopack -p 3000 -H 0.0.0.0", "dev:original": "next dev --turbopack -p 50513 -H 0.0.0.0", "build": "next build", "start": "next start -p 3000 -H 0.0.0.0", "start:prod": "next start -p 52580 -H 0.0.0.0", "staging": "NODE_ENV=staging next start -p 12001 -H 0.0.0.0", "lint": "next lint", "lint:fix": "next lint --fix", "check-types": "tsc --noEmit", "test": "jest --testTimeout=10000", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf .next dist node_modules/.cache", "deploy": "vercel --prod", "deploy:beta": "vercel --prod --target production", "preview": "vercel"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/is-prop-valid": "^1.3.1", "@headlessui/react": "^2.0.0", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tanstack/react-query": "^5.83.0", "@types/d3": "^7.4.3", "@types/leaflet": "^1.9.20", "@types/mapbox-gl": "^3.4.1", "@types/react-grid-layout": "^1.3.5", "@types/react-virtualized": "^9.21.29", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "chart.js": "^4.5.0", "chartjs-chart-sankey": "^0.14.0", "chartjs-chart-treemap": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.8.5", "date-fns": "^4.1.0", "framer-motion": "^12.19.2", "leaflet": "^1.9.4", "lucide-react": "^0.526.0", "mapbox-gl": "^3.13.0", "next": "15.4.4", "next-intl": "^4.3.1", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.2", "react-hook-form": "^7.59.0", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "react-map-gl": "^7.1.9", "react-resizable-panels": "^3.0.3", "react-virtualized": "^9.22.5", "recharts": "^2.13.3", "socket.io-client": "^4.7.4", "stripe": "^18.2.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.67", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^22", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@types/react-grid-layout": "^1.3.5", "eslint": "^9", "eslint-config-next": "15.4.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "tailwindcss": "^4", "tempo-devtools": "^2.0.108", "typescript": "^5", "vercel": "^44.4.1"}, "jest": {"testEnvironment": "jsdom", "setupFilesAfterEnv": ["<rootDir>/jest.setup.js"], "collectCoverageFrom": ["src/components/**/*.{js,ts,jsx,tsx}", "src/hooks/**/*.{js,ts,jsx,tsx}", "src/lib/**/*.{js,ts,jsx,tsx}", "!**/*.d.ts", "!**/node_modules/**"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/src/$1"}, "testPathIgnorePatterns": ["<rootDir>/.next/", "<rootDir>/node_modules/"]}, "engines": {"node": ">=18.0.0"}}