Requirement already satisfied: geoip2 in ./venv/lib/python3.12/site-packages (5.1.0)
Requirement already satisfied: aiohttp<4.0.0,>=3.6.2 in ./venv/lib/python3.12/site-packages (from geoip2) (3.12.15)
Requirement already satisfied: maxminddb<3.0.0,>=2.7.0 in ./venv/lib/python3.12/site-packages (from geoip2) (2.8.2)
Requirement already satisfied: requests<3.0.0,>=2.24.0 in ./venv/lib/python3.12/site-packages (from geoip2) (2.32.4)
Requirement already satisfied: aiohappyeyeballs>=2.5.0 in ./venv/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.6.2->geoip2) (2.6.1)
Requirement already satisfied: aiosignal>=1.4.0 in ./venv/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.6.2->geoip2) (1.4.0)
Requirement already satisfied: attrs>=17.3.0 in ./venv/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.6.2->geoip2) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in ./venv/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.6.2->geoip2) (1.7.0)
Requirement already satisfied: multidict<7.0,>=4.5 in ./venv/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.6.2->geoip2) (6.6.3)
Requirement already satisfied: propcache>=0.2.0 in ./venv/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.6.2->geoip2) (0.3.2)
Requirement already satisfied: yarl<2.0,>=1.17.0 in ./venv/lib/python3.12/site-packages (from aiohttp<4.0.0,>=3.6.2->geoip2) (1.20.1)
Requirement already satisfied: charset_normalizer<4,>=2 in ./venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.24.0->geoip2) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in ./venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.24.0->geoip2) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.24.0->geoip2) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in ./venv/lib/python3.12/site-packages (from requests<3.0.0,>=2.24.0->geoip2) (2025.7.14)
Requirement already satisfied: typing-extensions>=4.2 in ./venv/lib/python3.12/site-packages (from aiosignal>=1.4.0->aiohttp<4.0.0,>=3.6.2->geoip2) (4.14.1)
