"""
BiteBase Intelligence - Simplified Main FastAPI Application
Simplified version for development without complex dependencies
"""

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
import logging
from contextlib import asynccontextmanager

from app.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting BiteBase Intelligence API (Simplified)...")
    logger.info("Database initialization skipped for simplified version")
    
    yield
    
    # Shutdown
    logger.info("Shutting down BiteBase Intelligence API...")

# Create FastAPI application
app = FastAPI(
    title="BiteBase Intelligence API",
    description="AI-powered restaurant intelligence and location analytics platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "BiteBase Intelligence API (Simplified)",
        "version": "1.0.0",
        "status": "operational",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "bitebase-intelligence-api",
        "version": "1.0.0",
        "mode": "simplified"
    }

# Basic authentication endpoints
@app.post("/api/v1/auth/login")
async def login():
    """Mock login endpoint"""
    return {
        "success": True,
        "message": "Login successful",
        "data": {
            "user": {
                "id": "user_123",
                "email": "<EMAIL>",
                "firstName": "Demo",
                "lastName": "User",
                "role": "user"
            },
            "tokens": {
                "accessToken": "mock_access_token",
                "refreshToken": "mock_refresh_token",
                "expiresIn": "7d"
            }
        },
        "timestamp": "2025-01-01T00:00:00Z"
    }

@app.post("/api/v1/auth/register")
async def register():
    """Mock register endpoint"""
    return {
        "success": True,
        "message": "Registration successful",
        "data": {
            "user": {
                "id": "user_new_123",
                "email": "<EMAIL>",
                "firstName": "New",
                "lastName": "User",
                "role": "user"
            },
            "tokens": {
                "accessToken": "mock_access_token_new",
                "refreshToken": "mock_refresh_token_new",
                "expiresIn": "7d"
            }
        },
        "timestamp": "2025-01-01T00:00:00Z"
    }

@app.get("/api/v1/auth/me")
async def get_current_user():
    """Mock current user endpoint"""
    return {
        "success": True,
        "message": "User retrieved successfully",
        "data": {
            "user": {
                "id": "user_123",
                "email": "<EMAIL>",
                "firstName": "Demo",
                "lastName": "User",
                "role": "user"
            }
        },
        "timestamp": "2025-01-01T00:00:00Z"
    }

@app.get("/api/v1/analytics/dashboard")
async def get_dashboard():
    """Mock dashboard endpoint"""
    return {
        "success": True,
        "message": "Dashboard data retrieved successfully",
        "data": {
            "metrics": {
                "totalRevenue": 125000,
                "totalOrders": 2340,
                "averageOrderValue": 53.42,
                "customerSatisfaction": 4.7
            },
            "charts": {
                "revenue": [10000, 12000, 15000, 18000, 20000],
                "orders": [200, 220, 250, 280, 300]
            }
        },
        "timestamp": "2025-01-01T00:00:00Z"
    }

@app.get("/api/v1/restaurants")
async def get_restaurants():
    """Mock restaurants endpoint"""
    return {
        "success": True,
        "message": "Restaurants retrieved successfully",
        "data": [
            {
                "id": "restaurant_1",
                "name": "Demo Restaurant",
                "location": "San Francisco, CA",
                "type": "Italian",
                "rating": 4.5
            },
            {
                "id": "restaurant_2", 
                "name": "Test Bistro",
                "location": "New York, NY",
                "type": "French",
                "rating": 4.3
            }
        ],
        "timestamp": "2025-01-01T00:00:00Z"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
