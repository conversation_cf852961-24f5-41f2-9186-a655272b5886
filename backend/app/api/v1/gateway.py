"""
API Gateway & Developer Platform
RESTful APIs, GraphQL endpoints, webhooks, and developer documentation portal
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Response, BackgroundTasks
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, <PERSON>
from datetime import datetime, timedelta
import json
import asyncio
import logging

from ...auth.auth_manager import (
    get_current_user, get_current_tenant, validate_api_key_auth,
    require_permission, auth_manager, api_key_manager
)
from ...models.auth import User, Tenant, APIKey
from ...core.rate_limiter import RateLimiter
from ...core.webhook_manager import WebhookManager
from ...core.analytics import APIAnalytics
from ...database import get_db
from ...core.config import settings

logger = logging.getLogger(__name__)

# Initialize components
rate_limiter = RateLimiter()
webhook_manager = WebhookManager()
api_analytics = APIAnalytics()

# API Router
api_router = APIRouter(prefix="/api/v1", tags=["API Gateway"])


# Request/Response Models
class APIResponse(BaseModel):
    """Standard API response format"""
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class PaginationParams(BaseModel):
    """Standard pagination parameters"""
    page: int = Field(1, ge=1, description="Page number")
    limit: int = Field(20, ge=1, le=100, description="Items per page")
    sort_by: Optional[str] = Field(None, description="Sort field")
    sort_order: Optional[str] = Field("asc", regex="^(asc|desc)$")


class LocationAnalysisRequest(BaseModel):
    """Location analysis request model"""
    latitude: float = Field(..., ge=-90, le=90)
    longitude: float = Field(..., ge=-180, le=180)
    radius_km: float = Field(2.0, ge=0.1, le=50)
    analysis_type: str = Field("comprehensive", regex="^(demographic|competitive|comprehensive)$")
    include_visuals: bool = True


class LocationAnalysisResponse(BaseModel):
    """Location analysis response model"""
    location: Dict[str, float]
    radius_km: float
    analysis_type: str
    demographic_score: float
    competition_level: str
    foot_traffic: str
    market_potential: str
    insights: List[str]
    recommendations: List[str]
    visualization_url: Optional[str] = None


class MarketReportRequest(BaseModel):
    """Market report generation request"""
    location: Dict[str, float]
    report_type: str = Field("market_overview", regex="^(market_overview|competitive_analysis|demographic_study|opportunity_assessment)$")
    timeframe: str = Field("current", regex="^(current|6_months|12_months|24_months)$")
    format: str = Field("detailed", regex="^(summary|detailed|executive)$")
    custom_parameters: Optional[Dict[str, Any]] = None


class CompetitorSearchRequest(BaseModel):
    """Competitor search request"""
    location: Dict[str, float]
    radius_km: float = Field(3.0, ge=0.1, le=50)
    cuisine_type: Optional[str] = None
    price_range: Optional[str] = Field(None, regex="^(\$|\$\$|\$\$\$|\$\$\$\$)$")
    min_rating: Optional[float] = Field(None, ge=0, le=5)
    include_analysis: bool = True


class WebhookRequest(BaseModel):
    """Webhook registration request"""
    url: str = Field(..., regex=r'^https?://.+')
    events: List[str]
    secret: Optional[str] = None
    active: bool = True
    description: Optional[str] = None


# Middleware for API analytics and rate limiting
@api_router.middleware("http")
async def api_middleware(request: Request, call_next):
    """API Gateway middleware for analytics, rate limiting, and security"""
    start_time = datetime.utcnow()
    
    # Extract API key or user context
    api_key = request.headers.get('X-API-Key')
    auth_header = request.headers.get('Authorization')
    
    tenant_id = None
    user_id = None
    auth_type = 'anonymous'
    
    try:
        if api_key:
            # API Key authentication
            db = next(get_db())
            key_info = api_key_manager.validate_api_key(api_key, db)
            if key_info:
                tenant_id = str(key_info.tenant_id)
                auth_type = 'api_key'
        elif auth_header and auth_header.startswith('Bearer '):
            # JWT authentication
            token = auth_header.split(' ')[1]
            payload = auth_manager.verify_token(token)
            user_id = payload.get('user_id')
            tenant_id = payload.get('tenant_id')
            auth_type = 'jwt'
        
        # Rate limiting
        if tenant_id:
            rate_limit_key = f"api_rate_limit:{tenant_id}"
            if not await rate_limiter.check_rate_limit(rate_limit_key, limit=1000, window=3600):
                return JSONResponse(
                    status_code=429,
                    content={"error": "Rate limit exceeded", "retry_after": 3600}
                )
        
        # Process request
        response = await call_next(request)
        
        # Log API usage
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        await api_analytics.log_request(
            method=request.method,
            path=str(request.url.path),
            status_code=response.status_code,
            duration=duration,
            tenant_id=tenant_id,
            user_id=user_id,
            auth_type=auth_type,
            ip_address=request.client.host,
            user_agent=request.headers.get('User-Agent')
        )
        
        return response
        
    except Exception as e:
        logger.error(f"API middleware error: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "Internal server error"}
        )


# Location Analysis Endpoints
@api_router.post("/locations/analyze", response_model=APIResponse)
async def analyze_location(
    request: LocationAnalysisRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
    _: User = Depends(require_permission("locations", "analyze"))
):
    """Analyze restaurant location potential"""
    try:
        from ...services.location_service import LocationAnalysisService
        
        service = LocationAnalysisService()
        analysis = await service.analyze_location(
            latitude=request.latitude,
            longitude=request.longitude,
            radius_km=request.radius_km,
            analysis_type=request.analysis_type,
            tenant_id=str(tenant.id),
            include_visuals=request.include_visuals
        )
        
        # Trigger webhook if configured
        background_tasks.add_task(
            webhook_manager.trigger_webhook,
            tenant_id=str(tenant.id),
            event="location.analyzed",
            data=analysis
        )
        
        return APIResponse(
            success=True,
            data=LocationAnalysisResponse(**analysis),
            metadata={
                "request_id": f"loc_{datetime.utcnow().timestamp()}",
                "processing_time": "2.3s"
            }
        )
        
    except Exception as e:
        logger.error(f"Location analysis failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/reports/generate", response_model=APIResponse)
async def generate_market_report(
    request: MarketReportRequest,
    background_tasks: BackgroundTasks,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
    _: User = Depends(require_permission("reports", "generate"))
):
    """Generate comprehensive market analysis report"""
    try:
        from ...services.market_service import MarketResearchService
        
        service = MarketResearchService()
        report = await service.generate_report(
            location=request.location,
            report_type=request.report_type,
            timeframe=request.timeframe,
            format_type=request.format,
            tenant_id=str(tenant.id),
            custom_parameters=request.custom_parameters or {}
        )
        
        # Trigger webhook
        background_tasks.add_task(
            webhook_manager.trigger_webhook,
            tenant_id=str(tenant.id),
            event="report.generated",
            data={"report_id": report["id"], "type": request.report_type}
        )
        
        return APIResponse(
            success=True,
            data=report,
            metadata={
                "report_id": report["id"],
                "estimated_completion": "5-10 minutes"
            }
        )
        
    except Exception as e:
        logger.error(f"Report generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.post("/competitors/search", response_model=APIResponse)
async def search_competitors(
    request: CompetitorSearchRequest,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
    _: User = Depends(require_permission("competitors", "search"))
):
    """Search and analyze competitors in specified area"""
    try:
        from ...services.competitor_service import CompetitorAnalysisService
        
        service = CompetitorAnalysisService()
        competitors = await service.search_competitors(
            latitude=request.location["latitude"],
            longitude=request.location["longitude"],
            radius_km=request.radius_km,
            cuisine_type=request.cuisine_type,
            price_range=request.price_range,
            min_rating=request.min_rating,
            tenant_id=str(tenant.id),
            include_analysis=request.include_analysis
        )
        
        return APIResponse(
            success=True,
            data={
                "competitors": competitors,
                "search_parameters": request.dict(),
                "total_found": len(competitors)
            }
        )
        
    except Exception as e:
        logger.error(f"Competitor search failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Analytics Endpoints
@api_router.get("/analytics/insights", response_model=APIResponse)
async def get_business_insights(
    data_type: str = "performance",
    timeframe: str = "month",
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
    _: User = Depends(require_permission("analytics", "read"))
):
    """Get AI-powered business insights"""
    try:
        from ...services.analytics_service import BusinessAnalyticsService
        
        service = BusinessAnalyticsService()
        insights = await service.generate_insights(
            data_type=data_type,
            timeframe=timeframe,
            tenant_id=str(tenant.id)
        )
        
        return APIResponse(
            success=True,
            data=insights
        )
        
    except Exception as e:
        logger.error(f"Business insights generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Webhook Management
@api_router.post("/webhooks", response_model=APIResponse)
async def create_webhook(
    request: WebhookRequest,
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
    _: User = Depends(require_permission("webhooks", "create"))
):
    """Register a new webhook endpoint"""
    try:
        webhook = await webhook_manager.create_webhook(
            tenant_id=str(tenant.id),
            url=request.url,
            events=request.events,
            secret=request.secret,
            active=request.active,
            description=request.description
        )
        
        return APIResponse(
            success=True,
            data=webhook
        )
        
    except Exception as e:
        logger.error(f"Webhook creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/webhooks", response_model=APIResponse)
async def list_webhooks(
    pagination: PaginationParams = Depends(),
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
    _: User = Depends(require_permission("webhooks", "read"))
):
    """List all webhooks for the tenant"""
    try:
        webhooks = await webhook_manager.list_webhooks(
            tenant_id=str(tenant.id),
            page=pagination.page,
            limit=pagination.limit
        )
        
        return APIResponse(
            success=True,
            data=webhooks
        )
        
    except Exception as e:
        logger.error(f"Webhook listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# API Key Management
@api_router.post("/api-keys", response_model=APIResponse)
async def create_api_key(
    name: str,
    permissions: List[str],
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
    _: User = Depends(require_permission("api_keys", "create"))
):
    """Create a new API key"""
    try:
        db = next(get_db())
        api_key = api_key_manager.generate_api_key(
            tenant_id=str(tenant.id),
            name=name,
            permissions=permissions,
            db=db
        )
        
        return APIResponse(
            success=True,
            data={
                "api_key": api_key,
                "name": name,
                "permissions": permissions,
                "created_at": datetime.utcnow().isoformat()
            },
            metadata={
                "warning": "Store this API key securely. It will not be shown again."
            }
        )
        
    except Exception as e:
        logger.error(f"API key creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@api_router.get("/api-keys", response_model=APIResponse)
async def list_api_keys(
    user: User = Depends(get_current_user),
    tenant: Tenant = Depends(get_current_tenant),
    _: User = Depends(require_permission("api_keys", "read"))
):
    """List all API keys for the tenant"""
    try:
        db = next(get_db())
        api_keys = db.query(APIKey).filter(
            APIKey.tenant_id == tenant.id,
            APIKey.is_active == True
        ).all()
        
        return APIResponse(
            success=True,
            data=[{
                "id": str(key.id),
                "name": key.name,
                "key_prefix": key.key_prefix,
                "permissions": key.permissions,
                "last_used": key.last_used.isoformat() if key.last_used else None,
                "created_at": key.created_at.isoformat()
            } for key in api_keys]
        )
        
    except Exception as e:
        logger.error(f"API key listing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# API Documentation and Health
@api_router.get("/health", response_model=APIResponse)
async def health_check():
    """API health check endpoint"""
    return APIResponse(
        success=True,
        data={
            "status": "healthy",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat()
        }
    )


@api_router.get("/docs/openapi.json")
async def get_openapi_spec():
    """Get OpenAPI specification for API documentation"""
    from fastapi.openapi.utils import get_openapi
    from ...main import app
    
    return get_openapi(
        title="BiteBase Intelligence API",
        version="1.0.0",
        description="Production-ready AI SaaS platform for restaurant intelligence",
        routes=app.routes,
    )


# Error Handlers
@api_router.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions with consistent format"""
    return JSONResponse(
        status_code=exc.status_code,
        content=APIResponse(
            success=False,
            error=exc.detail,
            metadata={
                "status_code": exc.status_code,
                "path": str(request.url.path)
            }
        ).dict()
    )


@api_router.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content=APIResponse(
            success=False,
            error="Internal server error",
            metadata={
                "status_code": 500,
                "path": str(request.url.path)
            }
        ).dict()
    )
