"""
Security & Compliance
SOC2, GDPR compliance, data encryption, audit logging, and security scanning
"""

import hashlib
import secrets
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass
import json
import asyncio

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from sqlalchemy.orm import Session
from fastapi import Request, HTTPException
import ipaddress

from ..core.config import settings
from ..core.redis_client import redis_client
from ..database import get_db
from ..models.auth import User, Tenant, AuditLog

logger = logging.getLogger(__name__)


class ComplianceFramework(Enum):
    """Supported compliance frameworks"""
    SOC2 = "soc2"
    GDPR = "gdpr"
    HIPAA = "hipaa"
    PCI_DSS = "pci_dss"
    ISO27001 = "iso27001"


class DataClassification(Enum):
    """Data classification levels"""
    PUBLIC = "public"
    INTERNAL = "internal"
    CONFIDENTIAL = "confidential"
    RESTRICTED = "restricted"


class AuditEventType(Enum):
    """Audit event types for compliance"""
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    DATA_DELETION = "data_deletion"
    PERMISSION_CHANGE = "permission_change"
    SYSTEM_CONFIG_CHANGE = "system_config_change"
    SECURITY_INCIDENT = "security_incident"
    COMPLIANCE_VIOLATION = "compliance_violation"


@dataclass
class SecurityPolicy:
    """Security policy configuration"""
    name: str
    description: str
    framework: ComplianceFramework
    controls: List[str]
    requirements: Dict[str, Any]
    enabled: bool = True


@dataclass
class AuditEvent:
    """Structured audit event"""
    event_type: AuditEventType
    user_id: Optional[str]
    tenant_id: Optional[str]
    resource_type: str
    resource_id: Optional[str]
    action: str
    outcome: str  # success, failure, error
    ip_address: Optional[str]
    user_agent: Optional[str]
    details: Dict[str, Any]
    timestamp: datetime
    session_id: Optional[str] = None
    risk_score: Optional[int] = None


class EncryptionManager:
    """Data encryption and key management"""
    
    def __init__(self):
        self.master_key = settings.ENCRYPTION_KEY.encode()
        self.fernet = Fernet(self.master_key)
    
    def encrypt_data(self, data: Union[str, bytes], classification: DataClassification = DataClassification.CONFIDENTIAL) -> str:
        """Encrypt sensitive data"""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self.fernet.encrypt(data)
            
            # Add metadata for compliance tracking
            metadata = {
                'classification': classification.value,
                'encrypted_at': datetime.utcnow().isoformat(),
                'algorithm': 'Fernet',
                'key_version': '1'
            }
            
            return json.dumps({
                'data': encrypted_data.decode('utf-8'),
                'metadata': metadata
            })
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise HTTPException(status_code=500, detail="Encryption failed")
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data"""
        try:
            data_obj = json.loads(encrypted_data)
            encrypted_bytes = data_obj['data'].encode('utf-8')
            
            decrypted_data = self.fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise HTTPException(status_code=500, detail="Decryption failed")
    
    def hash_pii(self, data: str, salt: Optional[str] = None) -> str:
        """Hash PII data for pseudonymization"""
        if salt is None:
            salt = secrets.token_hex(16)
        
        combined = f"{data}{salt}".encode('utf-8')
        hash_obj = hashlib.sha256(combined)
        
        return f"{hash_obj.hexdigest()}:{salt}"
    
    def generate_data_key(self) -> str:
        """Generate new data encryption key"""
        return Fernet.generate_key().decode('utf-8')


class AuditLogger:
    """Comprehensive audit logging for compliance"""
    
    def __init__(self):
        self.encryption_manager = EncryptionManager()
    
    async def log_event(
        self,
        event: AuditEvent,
        compliance_frameworks: List[ComplianceFramework] = None
    ):
        """Log audit event with compliance requirements"""
        try:
            db = next(get_db())
            
            # Calculate risk score if not provided
            if event.risk_score is None:
                event.risk_score = self._calculate_risk_score(event)
            
            # Create audit log entry
            audit_log = AuditLog(
                event_type=event.event_type.value,
                user_id=event.user_id,
                tenant_id=event.tenant_id,
                resource_type=event.resource_type,
                resource_id=event.resource_id,
                action=event.action,
                outcome=event.outcome,
                ip_address=event.ip_address,
                user_agent=event.user_agent,
                session_id=event.session_id,
                risk_score=event.risk_score,
                details=event.details,
                timestamp=event.timestamp,
                created_at=datetime.utcnow()
            )
            
            # Encrypt sensitive details if required
            if event.event_type in [AuditEventType.DATA_ACCESS, AuditEventType.DATA_MODIFICATION]:
                audit_log.details = json.loads(self.encryption_manager.encrypt_data(
                    json.dumps(event.details),
                    DataClassification.CONFIDENTIAL
                ))
            
            db.add(audit_log)
            db.commit()
            
            # Store in Redis for real-time monitoring
            redis_key = f"audit:{event.tenant_id}:{datetime.now().strftime('%Y-%m-%d')}"
            await redis_client.lpush(redis_key, json.dumps({
                'event_type': event.event_type.value,
                'timestamp': event.timestamp.isoformat(),
                'risk_score': event.risk_score,
                'outcome': event.outcome
            }))
            await redis_client.expire(redis_key, 86400 * 7)  # Keep for 7 days
            
            # Check for compliance violations
            await self._check_compliance_violations(event, compliance_frameworks or [])
            
            logger.info(f"Audit event logged: {event.event_type.value} for user {event.user_id}")
            
        except Exception as e:
            logger.error(f"Failed to log audit event: {e}")
    
    def _calculate_risk_score(self, event: AuditEvent) -> int:
        """Calculate risk score for audit event"""
        base_score = 1
        
        # Event type risk
        risk_multipliers = {
            AuditEventType.USER_LOGIN: 1,
            AuditEventType.DATA_ACCESS: 2,
            AuditEventType.DATA_MODIFICATION: 3,
            AuditEventType.DATA_DELETION: 4,
            AuditEventType.PERMISSION_CHANGE: 3,
            AuditEventType.SYSTEM_CONFIG_CHANGE: 4,
            AuditEventType.SECURITY_INCIDENT: 5,
            AuditEventType.COMPLIANCE_VIOLATION: 5
        }
        
        score = base_score * risk_multipliers.get(event.event_type, 1)
        
        # Outcome risk
        if event.outcome == 'failure':
            score *= 2
        elif event.outcome == 'error':
            score *= 1.5
        
        # Time-based risk (off-hours access)
        hour = event.timestamp.hour
        if hour < 6 or hour > 22:  # Outside business hours
            score *= 1.3
        
        # IP-based risk (would integrate with threat intelligence)
        if event.ip_address and self._is_suspicious_ip(event.ip_address):
            score *= 2
        
        return min(int(score), 10)  # Cap at 10
    
    def _is_suspicious_ip(self, ip_address: str) -> bool:
        """Check if IP address is suspicious"""
        try:
            ip = ipaddress.ip_address(ip_address)
            
            # Check if it's a private IP
            if ip.is_private:
                return False
            
            # Would integrate with threat intelligence feeds
            # For now, just basic checks
            suspicious_ranges = [
                # Add known malicious IP ranges
            ]
            
            return False  # Placeholder
            
        except ValueError:
            return True  # Invalid IP format is suspicious
    
    async def _check_compliance_violations(
        self,
        event: AuditEvent,
        frameworks: List[ComplianceFramework]
    ):
        """Check for compliance violations"""
        violations = []
        
        for framework in frameworks:
            if framework == ComplianceFramework.GDPR:
                violations.extend(await self._check_gdpr_compliance(event))
            elif framework == ComplianceFramework.SOC2:
                violations.extend(await self._check_soc2_compliance(event))
        
        if violations:
            await self._handle_compliance_violations(event, violations)
    
    async def _check_gdpr_compliance(self, event: AuditEvent) -> List[str]:
        """Check GDPR compliance requirements"""
        violations = []
        
        # Data access without proper consent
        if event.event_type == AuditEventType.DATA_ACCESS:
            if not event.details.get('consent_verified'):
                violations.append("GDPR: Data access without verified consent")
        
        # Data retention violations
        if event.event_type == AuditEventType.DATA_MODIFICATION:
            if event.details.get('retention_period_exceeded'):
                violations.append("GDPR: Data retention period exceeded")
        
        return violations
    
    async def _check_soc2_compliance(self, event: AuditEvent) -> List[str]:
        """Check SOC2 compliance requirements"""
        violations = []
        
        # Access control violations
        if event.event_type == AuditEventType.PERMISSION_CHANGE:
            if not event.details.get('approval_required'):
                violations.append("SOC2: Permission change without proper approval")
        
        # Monitoring violations
        if event.risk_score > 7 and event.outcome == 'success':
            violations.append("SOC2: High-risk activity requires additional monitoring")
        
        return violations
    
    async def _handle_compliance_violations(
        self,
        event: AuditEvent,
        violations: List[str]
    ):
        """Handle detected compliance violations"""
        for violation in violations:
            # Log compliance violation
            violation_event = AuditEvent(
                event_type=AuditEventType.COMPLIANCE_VIOLATION,
                user_id=event.user_id,
                tenant_id=event.tenant_id,
                resource_type="compliance",
                resource_id=None,
                action="violation_detected",
                outcome="detected",
                ip_address=event.ip_address,
                user_agent=event.user_agent,
                details={
                    'violation': violation,
                    'original_event': event.event_type.value,
                    'severity': 'high'
                },
                timestamp=datetime.utcnow(),
                risk_score=8
            )
            
            await self.log_event(violation_event)
            
            # Send alert
            await self._send_compliance_alert(violation, event)
    
    async def _send_compliance_alert(self, violation: str, event: AuditEvent):
        """Send compliance violation alert"""
        alert_data = {
            'violation': violation,
            'event_type': event.event_type.value,
            'user_id': event.user_id,
            'tenant_id': event.tenant_id,
            'timestamp': event.timestamp.isoformat(),
            'severity': 'high'
        }
        
        # Store alert in Redis
        alert_key = f"compliance_alerts:{datetime.utcnow().timestamp()}"
        await redis_client.setex(alert_key, 86400, json.dumps(alert_data))
        
        logger.critical(f"Compliance violation detected: {violation}")


class DataPrivacyManager:
    """GDPR and data privacy compliance"""
    
    def __init__(self):
        self.encryption_manager = EncryptionManager()
        self.audit_logger = AuditLogger()
    
    async def process_data_subject_request(
        self,
        request_type: str,  # access, rectification, erasure, portability
        user_id: str,
        tenant_id: str,
        requester_id: str
    ) -> Dict[str, Any]:
        """Process GDPR data subject requests"""
        try:
            db = next(get_db())
            
            # Log the request
            await self.audit_logger.log_event(AuditEvent(
                event_type=AuditEventType.DATA_ACCESS,
                user_id=requester_id,
                tenant_id=tenant_id,
                resource_type="user_data",
                resource_id=user_id,
                action=f"gdpr_{request_type}_request",
                outcome="initiated",
                ip_address=None,
                user_agent=None,
                details={
                    'request_type': request_type,
                    'subject_user_id': user_id,
                    'gdpr_request': True
                },
                timestamp=datetime.utcnow()
            ))
            
            if request_type == "access":
                return await self._export_user_data(user_id, tenant_id)
            elif request_type == "erasure":
                return await self._delete_user_data(user_id, tenant_id)
            elif request_type == "portability":
                return await self._export_portable_data(user_id, tenant_id)
            elif request_type == "rectification":
                return {"status": "manual_review_required"}
            
        except Exception as e:
            logger.error(f"GDPR request processing failed: {e}")
            raise HTTPException(status_code=500, detail="Request processing failed")
    
    async def _export_user_data(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """Export all user data for GDPR access request"""
        db = next(get_db())
        
        # Get user data
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Collect all user-related data
        user_data = {
            'personal_info': {
                'email': user.email,
                'name': user.full_name,
                'created_at': user.created_at.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None
            },
            'audit_logs': [],
            'usage_records': [],
            'preferences': user.preferences or {}
        }
        
        # Get audit logs
        audit_logs = db.query(AuditLog).filter(AuditLog.user_id == user_id).limit(1000).all()
        for log in audit_logs:
            user_data['audit_logs'].append({
                'event_type': log.event_type,
                'action': log.action,
                'timestamp': log.timestamp.isoformat(),
                'outcome': log.outcome
            })
        
        return {
            'status': 'completed',
            'data': user_data,
            'export_date': datetime.utcnow().isoformat()
        }
    
    async def _delete_user_data(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """Delete user data for GDPR erasure request"""
        db = next(get_db())
        
        # Anonymize instead of hard delete for audit trail
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Anonymize personal data
        user.email = f"deleted_{user_id}@anonymized.local"
        user.full_name = "Deleted User"
        user.is_active = False
        user.deleted_at = datetime.utcnow()
        
        # Anonymize audit logs
        db.query(AuditLog).filter(AuditLog.user_id == user_id).update({
            'user_id': None,
            'details': {'anonymized': True}
        })
        
        db.commit()
        
        return {
            'status': 'completed',
            'deletion_date': datetime.utcnow().isoformat(),
            'note': 'Data anonymized for audit compliance'
        }
    
    async def _export_portable_data(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """Export user data in portable format"""
        # Similar to access request but in structured format
        return await self._export_user_data(user_id, tenant_id)


class SecurityScanner:
    """Security vulnerability scanning"""
    
    def __init__(self):
        self.scan_results: Dict[str, Any] = {}
    
    async def run_security_scan(self, scan_type: str = "full") -> Dict[str, Any]:
        """Run comprehensive security scan"""
        scan_id = secrets.token_hex(8)
        scan_start = datetime.utcnow()
        
        results = {
            'scan_id': scan_id,
            'scan_type': scan_type,
            'start_time': scan_start.isoformat(),
            'status': 'running',
            'vulnerabilities': [],
            'recommendations': []
        }
        
        try:
            # Database security scan
            db_results = await self._scan_database_security()
            results['vulnerabilities'].extend(db_results['vulnerabilities'])
            results['recommendations'].extend(db_results['recommendations'])
            
            # API security scan
            api_results = await self._scan_api_security()
            results['vulnerabilities'].extend(api_results['vulnerabilities'])
            results['recommendations'].extend(api_results['recommendations'])
            
            # Configuration scan
            config_results = await self._scan_configuration()
            results['vulnerabilities'].extend(config_results['vulnerabilities'])
            results['recommendations'].extend(config_results['recommendations'])
            
            results['status'] = 'completed'
            results['end_time'] = datetime.utcnow().isoformat()
            results['duration'] = (datetime.utcnow() - scan_start).total_seconds()
            
            # Store results
            self.scan_results[scan_id] = results
            
            return results
            
        except Exception as e:
            logger.error(f"Security scan failed: {e}")
            results['status'] = 'failed'
            results['error'] = str(e)
            return results
    
    async def _scan_database_security(self) -> Dict[str, Any]:
        """Scan database for security issues"""
        vulnerabilities = []
        recommendations = []
        
        try:
            db = next(get_db())
            
            # Check for weak passwords (would implement actual checks)
            # Check for unencrypted sensitive data
            # Check for proper access controls
            
            # Placeholder results
            recommendations.append({
                'category': 'database',
                'severity': 'medium',
                'description': 'Enable row-level security for all tenant data',
                'remediation': 'Implement RLS policies on all tenant tables'
            })
            
        except Exception as e:
            vulnerabilities.append({
                'category': 'database',
                'severity': 'high',
                'description': f'Database connection failed: {e}',
                'remediation': 'Check database connectivity and credentials'
            })
        
        return {
            'vulnerabilities': vulnerabilities,
            'recommendations': recommendations
        }
    
    async def _scan_api_security(self) -> Dict[str, Any]:
        """Scan API endpoints for security issues"""
        vulnerabilities = []
        recommendations = []
        
        # Check for common API vulnerabilities
        # - Missing authentication
        # - Insufficient rate limiting
        # - Information disclosure
        # - Input validation issues
        
        recommendations.append({
            'category': 'api',
            'severity': 'medium',
            'description': 'Implement API versioning strategy',
            'remediation': 'Use semantic versioning for API endpoints'
        })
        
        return {
            'vulnerabilities': vulnerabilities,
            'recommendations': recommendations
        }
    
    async def _scan_configuration(self) -> Dict[str, Any]:
        """Scan system configuration for security issues"""
        vulnerabilities = []
        recommendations = []
        
        # Check security headers
        # Check SSL/TLS configuration
        # Check environment variables
        # Check file permissions
        
        if not settings.SECURE_COOKIES:
            vulnerabilities.append({
                'category': 'configuration',
                'severity': 'medium',
                'description': 'Secure cookie flag not enabled',
                'remediation': 'Enable secure cookie settings in production'
            })
        
        return {
            'vulnerabilities': vulnerabilities,
            'recommendations': recommendations
        }


# Global instances
encryption_manager = EncryptionManager()
audit_logger = AuditLogger()
data_privacy_manager = DataPrivacyManager()
security_scanner = SecurityScanner()
