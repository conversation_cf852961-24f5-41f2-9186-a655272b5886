"""
Billing and Subscription Models
Stripe integration models for subscription management, usage tracking, and payment processing
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey, JSON, Numeric, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from decimal import Decimal
import uuid

from ..database import Base


class Subscription(Base):
    """Subscription management model"""
    __tablename__ = "subscriptions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    
    # Stripe integration
    stripe_subscription_id = Column(String(255), unique=True, nullable=False)
    stripe_customer_id = Column(String(255), nullable=True)
    
    # Subscription details
    plan_id = Column(String(50), nullable=False)  # starter, professional, enterprise
    billing_cycle = Column(String(20), nullable=False, default='monthly')  # monthly, yearly
    status = Column(String(50), nullable=False)  # active, past_due, cancelled, etc.
    
    # Billing periods
    current_period_start = Column(DateTime(timezone=True), nullable=False)
    current_period_end = Column(DateTime(timezone=True), nullable=False)
    trial_start = Column(DateTime(timezone=True), nullable=True)
    trial_end = Column(DateTime(timezone=True), nullable=True)
    
    # Pricing
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    
    # Subscription lifecycle
    started_at = Column(DateTime(timezone=True), nullable=False)
    cancelled_at = Column(DateTime(timezone=True), nullable=True)
    ended_at = Column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="subscriptions")
    invoices = relationship("Invoice", back_populates="subscription", cascade="all, delete-orphan")
    usage_records = relationship("UsageRecord", back_populates="subscription", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_subscriptions_tenant_id', 'tenant_id'),
        Index('idx_subscriptions_stripe_id', 'stripe_subscription_id'),
        Index('idx_subscriptions_status', 'status'),
        Index('idx_subscriptions_plan_id', 'plan_id'),
    )
    
    def __repr__(self):
        return f"<Subscription(id={self.id}, tenant_id={self.tenant_id}, plan={self.plan_id}, status={self.status})>"


class Invoice(Base):
    """Invoice tracking model"""
    __tablename__ = "invoices"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    subscription_id = Column(UUID(as_uuid=True), ForeignKey("subscriptions.id", ondelete="CASCADE"), nullable=True)
    
    # Stripe integration
    stripe_invoice_id = Column(String(255), unique=True, nullable=False)
    stripe_payment_intent_id = Column(String(255), nullable=True)
    
    # Invoice details
    invoice_number = Column(String(100), nullable=True)
    status = Column(String(50), nullable=False)  # draft, open, paid, void, uncollectible
    
    # Amounts
    subtotal = Column(Numeric(10, 2), nullable=False)
    tax = Column(Numeric(10, 2), nullable=False, default=0)
    total = Column(Numeric(10, 2), nullable=False)
    amount_paid = Column(Numeric(10, 2), nullable=False, default=0)
    amount_due = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    
    # Billing period
    period_start = Column(DateTime(timezone=True), nullable=True)
    period_end = Column(DateTime(timezone=True), nullable=True)
    
    # Payment details
    payment_method = Column(String(50), nullable=True)
    paid_at = Column(DateTime(timezone=True), nullable=True)
    due_date = Column(DateTime(timezone=True), nullable=True)
    
    # Invoice URLs
    hosted_invoice_url = Column(Text, nullable=True)
    invoice_pdf_url = Column(Text, nullable=True)
    
    # Metadata
    description = Column(Text, nullable=True)
    metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    subscription = relationship("Subscription", back_populates="invoices")
    invoice_items = relationship("InvoiceItem", back_populates="invoice", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_invoices_tenant_id', 'tenant_id'),
        Index('idx_invoices_subscription_id', 'subscription_id'),
        Index('idx_invoices_stripe_id', 'stripe_invoice_id'),
        Index('idx_invoices_status', 'status'),
        Index('idx_invoices_due_date', 'due_date'),
    )
    
    def __repr__(self):
        return f"<Invoice(id={self.id}, tenant_id={self.tenant_id}, total={self.total}, status={self.status})>"


class InvoiceItem(Base):
    """Individual line items on invoices"""
    __tablename__ = "invoice_items"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id", ondelete="CASCADE"), nullable=False)
    
    # Stripe integration
    stripe_invoice_item_id = Column(String(255), nullable=True)
    
    # Item details
    description = Column(Text, nullable=False)
    quantity = Column(Integer, nullable=False, default=1)
    unit_amount = Column(Numeric(10, 2), nullable=False)
    amount = Column(Numeric(10, 2), nullable=False)
    currency = Column(String(3), nullable=False, default='USD')
    
    # Item type
    item_type = Column(String(50), nullable=False)  # subscription, usage, one_time, discount
    
    # Usage-based billing
    usage_start = Column(DateTime(timezone=True), nullable=True)
    usage_end = Column(DateTime(timezone=True), nullable=True)
    
    # Metadata
    metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    invoice = relationship("Invoice", back_populates="invoice_items")
    
    # Indexes
    __table_args__ = (
        Index('idx_invoice_items_invoice_id', 'invoice_id'),
        Index('idx_invoice_items_type', 'item_type'),
    )
    
    def __repr__(self):
        return f"<InvoiceItem(id={self.id}, description={self.description}, amount={self.amount})>"


class UsageRecord(Base):
    """Usage tracking for billing purposes"""
    __tablename__ = "usage_records"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    subscription_id = Column(UUID(as_uuid=True), ForeignKey("subscriptions.id", ondelete="CASCADE"), nullable=True)
    
    # Usage details
    metric = Column(String(100), nullable=False)  # api_calls, reports, users, storage_gb
    quantity = Column(Integer, nullable=False)
    unit_price = Column(Numeric(10, 4), nullable=True)  # Price per unit if usage-based
    
    # Timing
    timestamp = Column(DateTime(timezone=True), nullable=False)
    billing_period_start = Column(DateTime(timezone=True), nullable=True)
    billing_period_end = Column(DateTime(timezone=True), nullable=True)
    
    # Billing status
    billed = Column(Boolean, default=False)
    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=True)
    
    # Context
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    resource_id = Column(String(255), nullable=True)  # ID of resource that generated usage
    
    # Metadata
    metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    subscription = relationship("Subscription", back_populates="usage_records")
    user = relationship("User")
    invoice = relationship("Invoice")
    
    # Indexes
    __table_args__ = (
        Index('idx_usage_records_tenant_id', 'tenant_id'),
        Index('idx_usage_records_subscription_id', 'subscription_id'),
        Index('idx_usage_records_metric', 'metric'),
        Index('idx_usage_records_timestamp', 'timestamp'),
        Index('idx_usage_records_billed', 'billed'),
        Index('idx_usage_records_billing_period', 'billing_period_start', 'billing_period_end'),
    )
    
    def __repr__(self):
        return f"<UsageRecord(id={self.id}, metric={self.metric}, quantity={self.quantity}, tenant_id={self.tenant_id})>"


class PaymentMethod(Base):
    """Customer payment methods"""
    __tablename__ = "payment_methods"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    
    # Stripe integration
    stripe_payment_method_id = Column(String(255), unique=True, nullable=False)
    
    # Payment method details
    type = Column(String(50), nullable=False)  # card, bank_account, etc.
    brand = Column(String(50), nullable=True)  # visa, mastercard, etc.
    last_four = Column(String(4), nullable=True)
    exp_month = Column(Integer, nullable=True)
    exp_year = Column(Integer, nullable=True)
    
    # Status
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    
    # Metadata
    metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    
    # Indexes
    __table_args__ = (
        Index('idx_payment_methods_tenant_id', 'tenant_id'),
        Index('idx_payment_methods_stripe_id', 'stripe_payment_method_id'),
        Index('idx_payment_methods_is_default', 'is_default'),
    )
    
    def __repr__(self):
        return f"<PaymentMethod(id={self.id}, type={self.type}, last_four={self.last_four}, tenant_id={self.tenant_id})>"


class BillingEvent(Base):
    """Billing event log for audit and debugging"""
    __tablename__ = "billing_events"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    
    # Event details
    event_type = Column(String(100), nullable=False)  # subscription_created, payment_succeeded, etc.
    event_source = Column(String(50), nullable=False)  # stripe, internal, api
    
    # Related objects
    subscription_id = Column(UUID(as_uuid=True), ForeignKey("subscriptions.id"), nullable=True)
    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=True)
    
    # Stripe event
    stripe_event_id = Column(String(255), nullable=True)
    
    # Event data
    event_data = Column(JSON, default=dict)
    
    # Processing status
    processed = Column(Boolean, default=False)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    event_timestamp = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    subscription = relationship("Subscription")
    invoice = relationship("Invoice")
    
    # Indexes
    __table_args__ = (
        Index('idx_billing_events_tenant_id', 'tenant_id'),
        Index('idx_billing_events_type', 'event_type'),
        Index('idx_billing_events_timestamp', 'event_timestamp'),
        Index('idx_billing_events_processed', 'processed'),
        Index('idx_billing_events_stripe_id', 'stripe_event_id'),
    )
    
    def __repr__(self):
        return f"<BillingEvent(id={self.id}, type={self.event_type}, tenant_id={self.tenant_id})>"


class Discount(Base):
    """Discounts and promotions"""
    __tablename__ = "discounts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=True)
    
    # Stripe integration
    stripe_coupon_id = Column(String(255), nullable=True)
    stripe_promotion_code_id = Column(String(255), nullable=True)
    
    # Discount details
    code = Column(String(100), unique=True, nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Discount type and amount
    discount_type = Column(String(20), nullable=False)  # percentage, fixed_amount
    amount = Column(Numeric(10, 2), nullable=False)  # Percentage (0-100) or fixed amount
    currency = Column(String(3), nullable=True)  # Required for fixed_amount discounts
    
    # Validity
    valid_from = Column(DateTime(timezone=True), nullable=False)
    valid_until = Column(DateTime(timezone=True), nullable=True)
    max_redemptions = Column(Integer, nullable=True)
    redemptions_count = Column(Integer, default=0)
    
    # Restrictions
    minimum_amount = Column(Numeric(10, 2), nullable=True)
    applicable_plans = Column(JSON, default=list)  # List of plan IDs
    first_time_customers_only = Column(Boolean, default=False)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Metadata
    metadata = Column(JSON, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant")
    
    # Indexes
    __table_args__ = (
        Index('idx_discounts_code', 'code'),
        Index('idx_discounts_tenant_id', 'tenant_id'),
        Index('idx_discounts_valid_period', 'valid_from', 'valid_until'),
        Index('idx_discounts_is_active', 'is_active'),
    )
    
    def __repr__(self):
        return f"<Discount(id={self.id}, code={self.code}, type={self.discount_type}, amount={self.amount})>"
