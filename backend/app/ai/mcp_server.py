"""
Production MCP Server for BiteBase Intelligence
Model Context Protocol server with enterprise AI tools and multi-tenant support
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass

from mcp import ClientSession, StdioServerParameters
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.types import (
    CallToolRequest, 
    CallToolResult, 
    ListToolsRequest, 
    ListToolsResult,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource
)

from ..database import get_db
from ..models.auth import User, Tenant
from ..services.location_service import LocationAnalysisService
from ..services.market_service import MarketResearchService
from ..services.competitor_service import CompetitorAnalysisService
from ..services.analytics_service import BusinessAnalyticsService
from ..core.config import settings
from ..core.redis_client import redis_client

logger = logging.getLogger(__name__)


@dataclass
class MCPContext:
    """Context information for MCP tool execution"""
    user_id: str
    tenant_id: str
    permissions: List[str]
    session_id: str
    request_id: str


class BiteBaseMCPServer:
    """Production MCP Server for BiteBase Intelligence Platform"""
    
    def __init__(self):
        self.server = Server("bitebase-intelligence")
        self.location_service = LocationAnalysisService()
        self.market_service = MarketResearchService()
        self.competitor_service = CompetitorAnalysisService()
        self.analytics_service = BusinessAnalyticsService()
        
        # Register tools
        self._register_tools()
        
        # Setup server handlers
        self.server.list_tools = self._list_tools
        self.server.call_tool = self._call_tool
    
    def _register_tools(self):
        """Register all available AI tools"""
        self.tools = {
            "analyze_location": Tool(
                name="analyze_location",
                description="Analyze restaurant location potential with demographic and competitive data",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "latitude": {"type": "number", "description": "Latitude coordinate"},
                        "longitude": {"type": "number", "description": "Longitude coordinate"},
                        "radius_km": {"type": "number", "default": 2, "description": "Analysis radius in kilometers"},
                        "analysis_type": {
                            "type": "string", 
                            "enum": ["demographic", "competitive", "comprehensive"],
                            "default": "comprehensive"
                        },
                        "include_visuals": {"type": "boolean", "default": True}
                    },
                    "required": ["latitude", "longitude"]
                }
            ),
            
            "generate_market_report": Tool(
                name="generate_market_report",
                description="Generate comprehensive market analysis report for restaurant planning",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "object",
                            "properties": {
                                "latitude": {"type": "number"},
                                "longitude": {"type": "number"}
                            },
                            "required": ["latitude", "longitude"]
                        },
                        "report_type": {
                            "type": "string",
                            "enum": ["market_overview", "competitive_analysis", "demographic_study", "opportunity_assessment"],
                            "default": "market_overview"
                        },
                        "timeframe": {
                            "type": "string",
                            "enum": ["current", "6_months", "12_months", "24_months"],
                            "default": "current"
                        },
                        "format": {
                            "type": "string",
                            "enum": ["summary", "detailed", "executive"],
                            "default": "detailed"
                        }
                    },
                    "required": ["location"]
                }
            ),
            
            "search_competitors": Tool(
                name="search_competitors",
                description="Search and analyze competitors in a specific area",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "object",
                            "properties": {
                                "latitude": {"type": "number"},
                                "longitude": {"type": "number"}
                            },
                            "required": ["latitude", "longitude"]
                        },
                        "radius_km": {"type": "number", "default": 3},
                        "cuisine_type": {"type": "string", "description": "Filter by cuisine type"},
                        "price_range": {
                            "type": "string",
                            "enum": ["$", "$$", "$$$", "$$$$"],
                            "description": "Filter by price range"
                        },
                        "min_rating": {"type": "number", "minimum": 0, "maximum": 5},
                        "include_analysis": {"type": "boolean", "default": True}
                    },
                    "required": ["location"]
                }
            ),
            
            "generate_business_insights": Tool(
                name="generate_business_insights",
                description="Generate AI-powered business insights and recommendations",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "data_type": {
                            "type": "string",
                            "enum": ["sales", "performance", "trends", "opportunities", "risks"],
                            "default": "performance"
                        },
                        "timeframe": {
                            "type": "string",
                            "enum": ["week", "month", "quarter", "year"],
                            "default": "month"
                        },
                        "metrics": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Specific metrics to focus on"
                        },
                        "include_predictions": {"type": "boolean", "default": True},
                        "confidence_threshold": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.7}
                    }
                }
            ),
            
            "optimize_dashboard": Tool(
                name="optimize_dashboard",
                description="Suggest optimal dashboard configuration based on user behavior and business goals",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "current_layout": {
                            "type": "object",
                            "description": "Current dashboard widget configuration"
                        },
                        "business_goals": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Primary business objectives"
                        },
                        "user_role": {"type": "string", "description": "User's role in organization"},
                        "usage_patterns": {
                            "type": "object",
                            "description": "Historical usage data"
                        }
                    },
                    "required": ["current_layout", "business_goals"]
                }
            ),
            
            "forecast_demand": Tool(
                name="forecast_demand",
                description="Forecast customer demand and foot traffic for restaurant locations",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "object",
                            "properties": {
                                "latitude": {"type": "number"},
                                "longitude": {"type": "number"}
                            },
                            "required": ["latitude", "longitude"]
                        },
                        "forecast_period": {
                            "type": "string",
                            "enum": ["week", "month", "quarter", "year"],
                            "default": "month"
                        },
                        "restaurant_type": {"type": "string", "description": "Type of restaurant"},
                        "historical_data": {
                            "type": "object",
                            "description": "Historical performance data if available"
                        }
                    },
                    "required": ["location"]
                }
            ),
            
            "analyze_demographics": Tool(
                name="analyze_demographics",
                description="Detailed demographic analysis for restaurant target market",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "object",
                            "properties": {
                                "latitude": {"type": "number"},
                                "longitude": {"type": "number"}
                            },
                            "required": ["latitude", "longitude"]
                        },
                        "radius_km": {"type": "number", "default": 2},
                        "target_segments": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Specific demographic segments to analyze"
                        },
                        "include_spending_patterns": {"type": "boolean", "default": True},
                        "include_lifestyle_data": {"type": "boolean", "default": True}
                    },
                    "required": ["location"]
                }
            ),
            
            "calculate_roi": Tool(
                name="calculate_roi",
                description="Calculate potential ROI for restaurant investment scenarios",
                inputSchema={
                    "type": "object",
                    "properties": {
                        "investment_amount": {"type": "number", "description": "Initial investment amount"},
                        "location": {
                            "type": "object",
                            "properties": {
                                "latitude": {"type": "number"},
                                "longitude": {"type": "number"}
                            },
                            "required": ["latitude", "longitude"]
                        },
                        "restaurant_concept": {
                            "type": "object",
                            "properties": {
                                "cuisine_type": {"type": "string"},
                                "price_range": {"type": "string"},
                                "seating_capacity": {"type": "number"},
                                "service_style": {"type": "string"}
                            }
                        },
                        "projection_years": {"type": "number", "default": 5, "minimum": 1, "maximum": 10}
                    },
                    "required": ["investment_amount", "location", "restaurant_concept"]
                }
            )
        }
    
    async def _list_tools(self, request: ListToolsRequest) -> ListToolsResult:
        """List available tools based on user permissions"""
        try:
            # Extract context from request (would be passed via headers/metadata)
            context = await self._extract_context(request)
            
            # Filter tools based on user permissions
            available_tools = []
            for tool_name, tool in self.tools.items():
                if await self._has_tool_permission(context, tool_name):
                    available_tools.append(tool)
            
            return ListToolsResult(tools=available_tools)
            
        except Exception as e:
            logger.error(f"Error listing tools: {e}")
            return ListToolsResult(tools=[])
    
    async def _call_tool(self, request: CallToolRequest) -> CallToolResult:
        """Execute tool with proper authentication and error handling"""
        try:
            # Extract and validate context
            context = await self._extract_context(request)
            
            # Validate permissions
            if not await self._has_tool_permission(context, request.params.name):
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"Insufficient permissions to use tool: {request.params.name}"
                    )],
                    isError=True
                )
            
            # Rate limiting check
            if not await self._check_rate_limit(context, request.params.name):
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text="Rate limit exceeded. Please try again later."
                    )],
                    isError=True
                )
            
            # Execute tool
            result = await self._execute_tool(context, request.params.name, request.params.arguments)
            
            # Log usage for analytics
            await self._log_tool_usage(context, request.params.name, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing tool {request.params.name}: {e}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Tool execution failed: {str(e)}"
                )],
                isError=True
            )
    
    async def _execute_tool(self, context: MCPContext, tool_name: str, arguments: Dict[str, Any]) -> CallToolResult:
        """Execute specific tool with arguments"""
        
        if tool_name == "analyze_location":
            return await self._analyze_location(context, arguments)
        elif tool_name == "generate_market_report":
            return await self._generate_market_report(context, arguments)
        elif tool_name == "search_competitors":
            return await self._search_competitors(context, arguments)
        elif tool_name == "generate_business_insights":
            return await self._generate_business_insights(context, arguments)
        elif tool_name == "optimize_dashboard":
            return await self._optimize_dashboard(context, arguments)
        elif tool_name == "forecast_demand":
            return await self._forecast_demand(context, arguments)
        elif tool_name == "analyze_demographics":
            return await self._analyze_demographics(context, arguments)
        elif tool_name == "calculate_roi":
            return await self._calculate_roi(context, arguments)
        else:
            raise ValueError(f"Unknown tool: {tool_name}")
    
    async def _analyze_location(self, context: MCPContext, args: Dict[str, Any]) -> CallToolResult:
        """Execute location analysis"""
        try:
            analysis = await self.location_service.analyze_location(
                latitude=args["latitude"],
                longitude=args["longitude"],
                radius_km=args.get("radius_km", 2),
                analysis_type=args.get("analysis_type", "comprehensive"),
                tenant_id=context.tenant_id
            )
            
            content = [TextContent(
                type="text",
                text=f"Location Analysis Results:\n\n"
                     f"📍 Location: {args['latitude']}, {args['longitude']}\n"
                     f"📊 Demographic Score: {analysis['demographic_score']}/100\n"
                     f"🏪 Competition Level: {analysis['competition_level']}\n"
                     f"👥 Foot Traffic: {analysis['foot_traffic']}\n"
                     f"💰 Market Potential: {analysis['market_potential']}\n\n"
                     f"🎯 Key Insights:\n" + "\n".join(f"• {insight}" for insight in analysis['insights'][:5]) +
                     f"\n\n📋 Recommendations:\n" + "\n".join(f"• {rec}" for rec in analysis['recommendations'][:3])
            )]
            
            if args.get("include_visuals") and analysis.get("visualization_data"):
                content.append(ImageContent(
                    type="image",
                    data=analysis["visualization_data"],
                    mimeType="image/png"
                ))
            
            return CallToolResult(content=content)
            
        except Exception as e:
            logger.error(f"Location analysis failed: {e}")
            raise
    
    async def _generate_market_report(self, context: MCPContext, args: Dict[str, Any]) -> CallToolResult:
        """Generate market research report"""
        try:
            report = await self.market_service.generate_report(
                location=args["location"],
                report_type=args.get("report_type", "market_overview"),
                timeframe=args.get("timeframe", "current"),
                format_type=args.get("format", "detailed"),
                tenant_id=context.tenant_id
            )
            
            content = [TextContent(
                type="text",
                text=f"Market Research Report\n"
                     f"{'=' * 50}\n\n"
                     f"📍 Location: {args['location']['latitude']}, {args['location']['longitude']}\n"
                     f"📊 Report Type: {args.get('report_type', 'market_overview').replace('_', ' ').title()}\n"
                     f"⏰ Timeframe: {args.get('timeframe', 'current').replace('_', ' ').title()}\n\n"
                     f"📋 Executive Summary:\n{report['executive_summary']}\n\n"
                     f"🔍 Key Findings:\n" + "\n".join(f"• {finding}" for finding in report['key_findings']) +
                     f"\n\n💡 Strategic Recommendations:\n" + "\n".join(f"• {rec}" for rec in report['recommendations']) +
                     f"\n\n📈 Market Metrics:\n" + "\n".join(f"• {k}: {v}" for k, v in report.get('metrics', {}).items())
            )]
            
            if report.get("download_url"):
                content.append(EmbeddedResource(
                    type="resource",
                    resource={
                        "uri": report["download_url"],
                        "name": f"market_report_{datetime.now().strftime('%Y%m%d')}.pdf",
                        "description": "Detailed market analysis report"
                    }
                ))
            
            return CallToolResult(content=content)
            
        except Exception as e:
            logger.error(f"Market report generation failed: {e}")
            raise
    
    async def _extract_context(self, request) -> MCPContext:
        """Extract user context from request metadata"""
        # This would extract from request headers/metadata in production
        # For now, return a mock context
        return MCPContext(
            user_id="user_123",
            tenant_id="tenant_456",
            permissions=["locations:analyze", "reports:generate", "competitors:search"],
            session_id="session_789",
            request_id=f"req_{datetime.now().timestamp()}"
        )
    
    async def _has_tool_permission(self, context: MCPContext, tool_name: str) -> bool:
        """Check if user has permission to use specific tool"""
        tool_permissions = {
            "analyze_location": "locations:analyze",
            "generate_market_report": "reports:generate",
            "search_competitors": "competitors:search",
            "generate_business_insights": "analytics:read",
            "optimize_dashboard": "dashboard:configure",
            "forecast_demand": "analytics:forecast",
            "analyze_demographics": "demographics:analyze",
            "calculate_roi": "analytics:calculate"
        }
        
        required_permission = tool_permissions.get(tool_name)
        return required_permission in context.permissions if required_permission else True
    
    async def _check_rate_limit(self, context: MCPContext, tool_name: str) -> bool:
        """Check rate limits for tool usage"""
        key = f"rate_limit:{context.tenant_id}:{context.user_id}:{tool_name}"
        current = await redis_client.get(key)
        
        # Different rate limits per tool
        limits = {
            "analyze_location": 100,  # per hour
            "generate_market_report": 10,
            "search_competitors": 50,
            "generate_business_insights": 20,
            "optimize_dashboard": 5,
            "forecast_demand": 15,
            "analyze_demographics": 30,
            "calculate_roi": 10
        }
        
        limit = limits.get(tool_name, 50)
        
        if current is None:
            await redis_client.setex(key, 3600, 1)
            return True
        
        if int(current) >= limit:
            return False
        
        await redis_client.incr(key)
        return True
    
    async def _log_tool_usage(self, context: MCPContext, tool_name: str, result: CallToolResult):
        """Log tool usage for analytics and billing"""
        usage_data = {
            "user_id": context.user_id,
            "tenant_id": context.tenant_id,
            "tool_name": tool_name,
            "success": not result.isError,
            "timestamp": datetime.utcnow().isoformat(),
            "request_id": context.request_id
        }
        
        # Store in Redis for real-time analytics
        await redis_client.lpush(f"tool_usage:{context.tenant_id}", json.dumps(usage_data))
        
        # Also log to database for permanent storage
        # This would be implemented with proper database logging
    
    async def run(self, transport_type: str = "stdio"):
        """Run the MCP server"""
        if transport_type == "stdio":
            async with self.server.run_stdio() as streams:
                await self.server.run(
                    streams[0], streams[1],
                    InitializationOptions(
                        server_name="bitebase-intelligence",
                        server_version="1.0.0",
                        capabilities={
                            "tools": {},
                            "logging": {},
                            "prompts": {}
                        }
                    )
                )


# Global server instance
mcp_server = BiteBaseMCPServer()


async def start_mcp_server():
    """Start the MCP server"""
    logger.info("Starting BiteBase MCP Server...")
    await mcp_server.run()


if __name__ == "__main__":
    asyncio.run(start_mcp_server())
