"""
Billing & Subscription Management
Stripe integration with usage-based billing, subscription tiers, and payment processing
"""

import stripe
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from decimal import Decimal
from dataclasses import dataclass

from sqlalchemy.orm import Session
from fastapi import HTTPException

from ..models.auth import Tenant, User
# from ..models.billing import Subscription, Invoice, UsageRecord, PaymentMethod
from ..core.config import settings
from ..core.redis_client import redis_client
from ..database import get_db

logger = logging.getLogger(__name__)

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


@dataclass
class PlanConfig:
    """Subscription plan configuration"""
    id: str
    name: str
    price_monthly: Decimal
    price_yearly: Decimal
    features: List[str]
    limits: Dict[str, int]
    stripe_price_id_monthly: str
    stripe_price_id_yearly: str


class BillingManager:
    """Comprehensive billing and subscription management"""
    
    def __init__(self):
        self.plans = {
            "starter": PlanConfig(
                id="starter",
                name="Starter",
                price_monthly=Decimal("29.00"),
                price_yearly=Decimal("290.00"),
                features=[
                    "Basic location analysis",
                    "Market reports (5/month)",
                    "Competitor search",
                    "Email support"
                ],
                limits={
                    "api_calls": 1000,
                    "reports": 5,
                    "users": 3,
                    "locations": 10
                },
                stripe_price_id_monthly="price_starter_monthly",
                stripe_price_id_yearly="price_starter_yearly"
            ),
            "professional": PlanConfig(
                id="professional",
                name="Professional",
                price_monthly=Decimal("99.00"),
                price_yearly=Decimal("990.00"),
                features=[
                    "Advanced location analysis",
                    "Unlimited market reports",
                    "Competitor intelligence",
                    "Demographics analysis",
                    "ROI calculator",
                    "Priority support",
                    "API access"
                ],
                limits={
                    "api_calls": 10000,
                    "reports": -1,  # Unlimited
                    "users": 10,
                    "locations": 100
                },
                stripe_price_id_monthly="price_professional_monthly",
                stripe_price_id_yearly="price_professional_yearly"
            ),
            "enterprise": PlanConfig(
                id="enterprise",
                name="Enterprise",
                price_monthly=Decimal("299.00"),
                price_yearly=Decimal("2990.00"),
                features=[
                    "Everything in Professional",
                    "Custom integrations",
                    "White-label options",
                    "Advanced analytics",
                    "Dedicated support",
                    "SLA guarantee",
                    "Custom reporting"
                ],
                limits={
                    "api_calls": 100000,
                    "reports": -1,
                    "users": -1,  # Unlimited
                    "locations": -1
                },
                stripe_price_id_monthly="price_enterprise_monthly",
                stripe_price_id_yearly="price_enterprise_yearly"
            )
        }
    
    async def create_customer(self, tenant: Tenant, user: User) -> str:
        """Create Stripe customer for tenant"""
        try:
            customer = stripe.Customer.create(
                email=user.email,
                name=tenant.name,
                metadata={
                    "tenant_id": str(tenant.id),
                    "user_id": str(user.id)
                }
            )
            
            # Update tenant with Stripe customer ID
            db = next(get_db())
            tenant.stripe_customer_id = customer.id
            db.commit()
            
            logger.info(f"Created Stripe customer {customer.id} for tenant {tenant.id}")
            return customer.id
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create Stripe customer: {e}")
            raise HTTPException(status_code=500, detail="Failed to create customer")
    
    async def create_subscription(
        self, 
        tenant_id: str, 
        plan_id: str, 
        billing_cycle: str = "monthly",
        payment_method_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create new subscription"""
        try:
            db = next(get_db())
            tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
            
            if not tenant:
                raise HTTPException(status_code=404, detail="Tenant not found")
            
            if not tenant.stripe_customer_id:
                raise HTTPException(status_code=400, detail="Customer not set up for billing")
            
            plan = self.plans.get(plan_id)
            if not plan:
                raise HTTPException(status_code=400, detail="Invalid plan")
            
            # Get Stripe price ID based on billing cycle
            price_id = plan.stripe_price_id_yearly if billing_cycle == "yearly" else plan.stripe_price_id_monthly
            
            # Create subscription in Stripe
            subscription_params = {
                "customer": tenant.stripe_customer_id,
                "items": [{"price": price_id}],
                "metadata": {
                    "tenant_id": tenant_id,
                    "plan_id": plan_id,
                    "billing_cycle": billing_cycle
                },
                "expand": ["latest_invoice.payment_intent"]
            }
            
            if payment_method_id:
                subscription_params["default_payment_method"] = payment_method_id
            
            stripe_subscription = stripe.Subscription.create(**subscription_params)
            
            # Create subscription record in database
            subscription = Subscription(
                tenant_id=tenant_id,
                stripe_subscription_id=stripe_subscription.id,
                plan_id=plan_id,
                billing_cycle=billing_cycle,
                status=stripe_subscription.status,
                current_period_start=datetime.fromtimestamp(stripe_subscription.current_period_start),
                current_period_end=datetime.fromtimestamp(stripe_subscription.current_period_end),
                created_at=datetime.utcnow()
            )
            
            db.add(subscription)
            
            # Update tenant plan
            tenant.plan_type = plan_id
            tenant.subscription_id = stripe_subscription.id
            tenant.status = "active"
            
            db.commit()
            
            logger.info(f"Created subscription {stripe_subscription.id} for tenant {tenant_id}")
            
            return {
                "subscription_id": stripe_subscription.id,
                "status": stripe_subscription.status,
                "client_secret": stripe_subscription.latest_invoice.payment_intent.client_secret if stripe_subscription.latest_invoice.payment_intent else None,
                "plan": plan.name,
                "billing_cycle": billing_cycle,
                "current_period_end": subscription.current_period_end.isoformat()
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create subscription: {e}")
            raise HTTPException(status_code=500, detail="Failed to create subscription")
    
    async def update_subscription(
        self, 
        tenant_id: str, 
        plan_id: str, 
        billing_cycle: Optional[str] = None
    ) -> Dict[str, Any]:
        """Update existing subscription"""
        try:
            db = next(get_db())
            tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
            
            if not tenant or not tenant.subscription_id:
                raise HTTPException(status_code=404, detail="Subscription not found")
            
            plan = self.plans.get(plan_id)
            if not plan:
                raise HTTPException(status_code=400, detail="Invalid plan")
            
            # Get current subscription
            stripe_subscription = stripe.Subscription.retrieve(tenant.subscription_id)
            
            # Determine new price ID
            current_billing_cycle = billing_cycle or ("yearly" if "yearly" in stripe_subscription.items.data[0].price.id else "monthly")
            price_id = plan.stripe_price_id_yearly if current_billing_cycle == "yearly" else plan.stripe_price_id_monthly
            
            # Update subscription in Stripe
            updated_subscription = stripe.Subscription.modify(
                tenant.subscription_id,
                items=[{
                    "id": stripe_subscription.items.data[0].id,
                    "price": price_id
                }],
                proration_behavior="create_prorations",
                metadata={
                    "tenant_id": tenant_id,
                    "plan_id": plan_id,
                    "billing_cycle": current_billing_cycle
                }
            )
            
            # Update database records
            subscription = db.query(Subscription).filter(
                Subscription.tenant_id == tenant_id,
                Subscription.stripe_subscription_id == tenant.subscription_id
            ).first()
            
            if subscription:
                subscription.plan_id = plan_id
                subscription.billing_cycle = current_billing_cycle
                subscription.status = updated_subscription.status
                subscription.updated_at = datetime.utcnow()
            
            tenant.plan_type = plan_id
            db.commit()
            
            logger.info(f"Updated subscription {tenant.subscription_id} for tenant {tenant_id}")
            
            return {
                "subscription_id": updated_subscription.id,
                "status": updated_subscription.status,
                "plan": plan.name,
                "billing_cycle": current_billing_cycle,
                "current_period_end": datetime.fromtimestamp(updated_subscription.current_period_end).isoformat()
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to update subscription: {e}")
            raise HTTPException(status_code=500, detail="Failed to update subscription")
    
    async def cancel_subscription(self, tenant_id: str, immediate: bool = False) -> Dict[str, Any]:
        """Cancel subscription"""
        try:
            db = next(get_db())
            tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
            
            if not tenant or not tenant.subscription_id:
                raise HTTPException(status_code=404, detail="Subscription not found")
            
            if immediate:
                # Cancel immediately
                stripe_subscription = stripe.Subscription.delete(tenant.subscription_id)
                tenant.status = "cancelled"
            else:
                # Cancel at period end
                stripe_subscription = stripe.Subscription.modify(
                    tenant.subscription_id,
                    cancel_at_period_end=True
                )
                tenant.status = "cancelling"
            
            # Update database
            subscription = db.query(Subscription).filter(
                Subscription.tenant_id == tenant_id,
                Subscription.stripe_subscription_id == tenant.subscription_id
            ).first()
            
            if subscription:
                subscription.status = stripe_subscription.status
                subscription.cancelled_at = datetime.utcnow()
                subscription.updated_at = datetime.utcnow()
            
            db.commit()
            
            logger.info(f"Cancelled subscription {tenant.subscription_id} for tenant {tenant_id}")
            
            return {
                "subscription_id": stripe_subscription.id,
                "status": stripe_subscription.status,
                "cancelled_at": datetime.utcnow().isoformat(),
                "access_until": datetime.fromtimestamp(stripe_subscription.current_period_end).isoformat() if not immediate else None
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to cancel subscription: {e}")
            raise HTTPException(status_code=500, detail="Failed to cancel subscription")
    
    async def record_usage(
        self, 
        tenant_id: str, 
        metric: str, 
        quantity: int, 
        timestamp: Optional[datetime] = None
    ):
        """Record usage for billing purposes"""
        try:
            db = next(get_db())
            
            usage_record = UsageRecord(
                tenant_id=tenant_id,
                metric=metric,
                quantity=quantity,
                timestamp=timestamp or datetime.utcnow(),
                created_at=datetime.utcnow()
            )
            
            db.add(usage_record)
            db.commit()
            
            # Also update Redis for real-time tracking
            key = f"usage:{tenant_id}:{metric}:{datetime.now().strftime('%Y-%m-%d')}"
            await redis_client.incrby(key, quantity)
            await redis_client.expire(key, 86400 * 7)  # Keep for 7 days
            
            logger.debug(f"Recorded usage: {metric}={quantity} for tenant {tenant_id}")
            
        except Exception as e:
            logger.error(f"Failed to record usage: {e}")
    
    async def get_usage_summary(self, tenant_id: str, period: str = "current") -> Dict[str, Any]:
        """Get usage summary for tenant"""
        try:
            db = next(get_db())
            
            # Determine date range
            if period == "current":
                tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
                if tenant and tenant.subscription_id:
                    subscription = stripe.Subscription.retrieve(tenant.subscription_id)
                    start_date = datetime.fromtimestamp(subscription.current_period_start)
                    end_date = datetime.fromtimestamp(subscription.current_period_end)
                else:
                    start_date = datetime.now().replace(day=1)
                    end_date = datetime.now()
            else:
                # Handle other periods (last_month, etc.)
                start_date = datetime.now().replace(day=1) - timedelta(days=30)
                end_date = datetime.now().replace(day=1)
            
            # Query usage records
            usage_records = db.query(UsageRecord).filter(
                UsageRecord.tenant_id == tenant_id,
                UsageRecord.timestamp >= start_date,
                UsageRecord.timestamp <= end_date
            ).all()
            
            # Aggregate usage by metric
            usage_summary = {}
            for record in usage_records:
                if record.metric not in usage_summary:
                    usage_summary[record.metric] = 0
                usage_summary[record.metric] += record.quantity
            
            # Get plan limits
            tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
            plan = self.plans.get(tenant.plan_type, self.plans["starter"])
            
            # Calculate usage percentages
            usage_with_limits = {}
            for metric, usage in usage_summary.items():
                limit = plan.limits.get(metric, 0)
                percentage = (usage / limit * 100) if limit > 0 else 0
                
                usage_with_limits[metric] = {
                    "usage": usage,
                    "limit": limit,
                    "percentage": min(percentage, 100),
                    "unlimited": limit == -1
                }
            
            return {
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "plan": plan.name,
                "usage": usage_with_limits,
                "total_api_calls": usage_summary.get("api_calls", 0),
                "total_reports": usage_summary.get("reports", 0)
            }
            
        except Exception as e:
            logger.error(f"Failed to get usage summary: {e}")
            raise HTTPException(status_code=500, detail="Failed to get usage summary")
    
    async def create_payment_method(
        self, 
        tenant_id: str, 
        payment_method_id: str
    ) -> Dict[str, Any]:
        """Attach payment method to customer"""
        try:
            db = next(get_db())
            tenant = db.query(Tenant).filter(Tenant.id == tenant_id).first()
            
            if not tenant or not tenant.stripe_customer_id:
                raise HTTPException(status_code=404, detail="Customer not found")
            
            # Attach payment method to customer
            payment_method = stripe.PaymentMethod.attach(
                payment_method_id,
                customer=tenant.stripe_customer_id
            )
            
            # Set as default payment method
            stripe.Customer.modify(
                tenant.stripe_customer_id,
                invoice_settings={"default_payment_method": payment_method_id}
            )
            
            # Store in database
            pm_record = PaymentMethod(
                tenant_id=tenant_id,
                stripe_payment_method_id=payment_method_id,
                type=payment_method.type,
                last_four=payment_method.card.last4 if payment_method.type == "card" else None,
                brand=payment_method.card.brand if payment_method.type == "card" else None,
                is_default=True,
                created_at=datetime.utcnow()
            )
            
            # Set other payment methods as non-default
            db.query(PaymentMethod).filter(
                PaymentMethod.tenant_id == tenant_id,
                PaymentMethod.is_default == True
            ).update({"is_default": False})
            
            db.add(pm_record)
            db.commit()
            
            logger.info(f"Added payment method {payment_method_id} for tenant {tenant_id}")
            
            return {
                "payment_method_id": payment_method_id,
                "type": payment_method.type,
                "last_four": payment_method.card.last4 if payment_method.type == "card" else None,
                "brand": payment_method.card.brand if payment_method.type == "card" else None,
                "is_default": True
            }
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create payment method: {e}")
            raise HTTPException(status_code=500, detail="Failed to add payment method")
    
    async def handle_webhook(self, event: Dict[str, Any]) -> bool:
        """Handle Stripe webhook events"""
        try:
            event_type = event["type"]
            data = event["data"]["object"]
            
            if event_type == "invoice.payment_succeeded":
                await self._handle_payment_succeeded(data)
            elif event_type == "invoice.payment_failed":
                await self._handle_payment_failed(data)
            elif event_type == "customer.subscription.updated":
                await self._handle_subscription_updated(data)
            elif event_type == "customer.subscription.deleted":
                await self._handle_subscription_deleted(data)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to handle webhook: {e}")
            return False
    
    async def _handle_payment_succeeded(self, invoice_data: Dict[str, Any]):
        """Handle successful payment"""
        subscription_id = invoice_data.get("subscription")
        if not subscription_id:
            return
        
        db = next(get_db())
        tenant = db.query(Tenant).filter(Tenant.subscription_id == subscription_id).first()
        
        if tenant:
            tenant.status = "active"
            db.commit()
            logger.info(f"Payment succeeded for tenant {tenant.id}")
    
    async def _handle_payment_failed(self, invoice_data: Dict[str, Any]):
        """Handle failed payment"""
        subscription_id = invoice_data.get("subscription")
        if not subscription_id:
            return
        
        db = next(get_db())
        tenant = db.query(Tenant).filter(Tenant.subscription_id == subscription_id).first()
        
        if tenant:
            tenant.status = "past_due"
            db.commit()
            logger.warning(f"Payment failed for tenant {tenant.id}")
    
    async def _handle_subscription_updated(self, subscription_data: Dict[str, Any]):
        """Handle subscription updates"""
        subscription_id = subscription_data["id"]
        
        db = next(get_db())
        subscription = db.query(Subscription).filter(
            Subscription.stripe_subscription_id == subscription_id
        ).first()
        
        if subscription:
            subscription.status = subscription_data["status"]
            subscription.current_period_start = datetime.fromtimestamp(subscription_data["current_period_start"])
            subscription.current_period_end = datetime.fromtimestamp(subscription_data["current_period_end"])
            subscription.updated_at = datetime.utcnow()
            db.commit()
    
    async def _handle_subscription_deleted(self, subscription_data: Dict[str, Any]):
        """Handle subscription cancellation"""
        subscription_id = subscription_data["id"]
        
        db = next(get_db())
        tenant = db.query(Tenant).filter(Tenant.subscription_id == subscription_id).first()
        
        if tenant:
            tenant.status = "cancelled"
            tenant.plan_type = "free"
            db.commit()
            logger.info(f"Subscription cancelled for tenant {tenant.id}")


# Global billing manager instance
billing_manager = BillingManager()
