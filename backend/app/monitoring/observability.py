"""
Monitoring & Observability
Comprehensive logging, metrics, alerting, and performance monitoring for production
"""

import logging
import time
import asyncio
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from contextlib import asynccontextmanager
from functools import wraps

import psutil
import aioredis
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry, generate_latest
from fastapi import Request, Response
from sqlalchemy import text
from sqlalchemy.orm import Session

from ..core.config import settings
from ..core.redis_client import redis_client
from ..database import get_db

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('/var/log/bitebase/app.log') if settings.ENVIRONMENT == 'production' else logging.NullHandler()
    ]
)

logger = logging.getLogger(__name__)


@dataclass
class MetricData:
    """Structured metric data"""
    name: str
    value: float
    labels: Dict[str, str]
    timestamp: datetime
    tenant_id: Optional[str] = None


@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    metric: str
    condition: str  # gt, lt, eq
    threshold: float
    duration: int  # seconds
    severity: str  # critical, warning, info
    channels: List[str]  # email, slack, webhook
    enabled: bool = True


class PrometheusMetrics:
    """Prometheus metrics collection"""
    
    def __init__(self):
        self.registry = CollectorRegistry()
        
        # HTTP metrics
        self.http_requests_total = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['method', 'endpoint', 'status_code', 'tenant_id'],
            registry=self.registry
        )
        
        self.http_request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['method', 'endpoint', 'tenant_id'],
            registry=self.registry
        )
        
        # Business metrics
        self.api_calls_total = Counter(
            'api_calls_total',
            'Total API calls',
            ['tenant_id', 'endpoint', 'auth_type'],
            registry=self.registry
        )
        
        self.location_analyses_total = Counter(
            'location_analyses_total',
            'Total location analyses',
            ['tenant_id', 'analysis_type'],
            registry=self.registry
        )
        
        self.reports_generated_total = Counter(
            'reports_generated_total',
            'Total reports generated',
            ['tenant_id', 'report_type'],
            registry=self.registry
        )
        
        # System metrics
        self.active_users = Gauge(
            'active_users_total',
            'Currently active users',
            ['tenant_id'],
            registry=self.registry
        )
        
        self.database_connections = Gauge(
            'database_connections_active',
            'Active database connections',
            registry=self.registry
        )
        
        self.redis_connections = Gauge(
            'redis_connections_active',
            'Active Redis connections',
            registry=self.registry
        )
        
        # Error metrics
        self.errors_total = Counter(
            'errors_total',
            'Total errors',
            ['error_type', 'tenant_id', 'endpoint'],
            registry=self.registry
        )
        
        # Performance metrics
        self.response_time_percentiles = Histogram(
            'response_time_percentiles',
            'Response time percentiles',
            ['endpoint', 'tenant_id'],
            buckets=[0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99, 1.0, 2.5, 5.0, 10.0],
            registry=self.registry
        )


class PerformanceMonitor:
    """Application performance monitoring"""
    
    def __init__(self):
        self.metrics = PrometheusMetrics()
        self.alert_rules: List[AlertRule] = []
        self.active_alerts: Dict[str, datetime] = {}
    
    async def track_request(
        self, 
        method: str, 
        endpoint: str, 
        status_code: int,
        duration: float,
        tenant_id: Optional[str] = None
    ):
        """Track HTTP request metrics"""
        labels = {
            'method': method,
            'endpoint': endpoint,
            'status_code': str(status_code),
            'tenant_id': tenant_id or 'anonymous'
        }
        
        self.metrics.http_requests_total.labels(**labels).inc()
        self.metrics.http_request_duration.labels(
            method=method,
            endpoint=endpoint,
            tenant_id=tenant_id or 'anonymous'
        ).observe(duration)
        
        # Track response time percentiles
        self.metrics.response_time_percentiles.labels(
            endpoint=endpoint,
            tenant_id=tenant_id or 'anonymous'
        ).observe(duration)
        
        # Log slow requests
        if duration > 5.0:
            logger.warning(f"Slow request: {method} {endpoint} took {duration:.2f}s")
    
    async def track_api_call(
        self, 
        tenant_id: str, 
        endpoint: str, 
        auth_type: str = 'jwt'
    ):
        """Track API usage"""
        self.metrics.api_calls_total.labels(
            tenant_id=tenant_id,
            endpoint=endpoint,
            auth_type=auth_type
        ).inc()
        
        # Store in Redis for real-time tracking
        key = f"api_usage:{tenant_id}:{datetime.now().strftime('%Y-%m-%d-%H')}"
        await redis_client.incr(key)
        await redis_client.expire(key, 86400)  # 24 hours
    
    async def track_business_metric(
        self, 
        metric_type: str, 
        tenant_id: str, 
        labels: Optional[Dict[str, str]] = None
    ):
        """Track business-specific metrics"""
        labels = labels or {}
        
        if metric_type == 'location_analysis':
            self.metrics.location_analyses_total.labels(
                tenant_id=tenant_id,
                analysis_type=labels.get('analysis_type', 'unknown')
            ).inc()
        elif metric_type == 'report_generated':
            self.metrics.reports_generated_total.labels(
                tenant_id=tenant_id,
                report_type=labels.get('report_type', 'unknown')
            ).inc()
    
    async def track_error(
        self, 
        error_type: str, 
        tenant_id: Optional[str] = None,
        endpoint: Optional[str] = None,
        error_details: Optional[Dict[str, Any]] = None
    ):
        """Track application errors"""
        self.metrics.errors_total.labels(
            error_type=error_type,
            tenant_id=tenant_id or 'system',
            endpoint=endpoint or 'unknown'
        ).inc()
        
        # Log error details
        logger.error(f"Error tracked: {error_type}", extra={
            'tenant_id': tenant_id,
            'endpoint': endpoint,
            'error_details': error_details
        })
        
        # Store in Redis for alerting
        error_key = f"errors:{error_type}:{datetime.now().strftime('%Y-%m-%d-%H')}"
        await redis_client.incr(error_key)
        await redis_client.expire(error_key, 86400)
    
    async def update_system_metrics(self):
        """Update system-level metrics"""
        try:
            # Database connections
            db = next(get_db())
            result = db.execute(text("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"))
            active_connections = result.scalar()
            self.metrics.database_connections.set(active_connections)
            
            # Redis info
            redis_info = await redis_client.info()
            self.metrics.redis_connections.set(redis_info.get('connected_clients', 0))
            
            # System resources
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('/').percent
            
            # Store system metrics in Redis
            system_metrics = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            await redis_client.setex(
                'system_metrics',
                300,  # 5 minutes
                json.dumps(system_metrics)
            )
            
        except Exception as e:
            logger.error(f"Failed to update system metrics: {e}")
    
    async def check_alerts(self):
        """Check alert conditions and trigger notifications"""
        try:
            for rule in self.alert_rules:
                if not rule.enabled:
                    continue
                
                # Get metric value from Redis
                metric_key = f"metrics:{rule.metric}"
                metric_value = await redis_client.get(metric_key)
                
                if metric_value is None:
                    continue
                
                metric_value = float(metric_value)
                
                # Check condition
                triggered = False
                if rule.condition == 'gt' and metric_value > rule.threshold:
                    triggered = True
                elif rule.condition == 'lt' and metric_value < rule.threshold:
                    triggered = True
                elif rule.condition == 'eq' and metric_value == rule.threshold:
                    triggered = True
                
                # Handle alert state
                alert_key = f"alert:{rule.name}"
                
                if triggered:
                    if alert_key not in self.active_alerts:
                        # New alert
                        self.active_alerts[alert_key] = datetime.utcnow()
                        await self._send_alert(rule, metric_value, 'triggered')
                        logger.warning(f"Alert triggered: {rule.name} - {metric_value}")
                    elif datetime.utcnow() - self.active_alerts[alert_key] > timedelta(seconds=rule.duration):
                        # Alert duration exceeded
                        await self._send_alert(rule, metric_value, 'escalated')
                else:
                    if alert_key in self.active_alerts:
                        # Alert resolved
                        del self.active_alerts[alert_key]
                        await self._send_alert(rule, metric_value, 'resolved')
                        logger.info(f"Alert resolved: {rule.name}")
                        
        except Exception as e:
            logger.error(f"Failed to check alerts: {e}")
    
    async def _send_alert(self, rule: AlertRule, value: float, status: str):
        """Send alert notification"""
        alert_data = {
            'rule_name': rule.name,
            'metric': rule.metric,
            'value': value,
            'threshold': rule.threshold,
            'severity': rule.severity,
            'status': status,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Store alert in Redis
        alert_key = f"alerts:{rule.name}:{datetime.utcnow().timestamp()}"
        await redis_client.setex(alert_key, 86400, json.dumps(alert_data))
        
        # Send notifications based on channels
        for channel in rule.channels:
            if channel == 'email':
                await self._send_email_alert(alert_data)
            elif channel == 'slack':
                await self._send_slack_alert(alert_data)
            elif channel == 'webhook':
                await self._send_webhook_alert(alert_data)
    
    async def _send_email_alert(self, alert_data: Dict[str, Any]):
        """Send email alert notification"""
        # Implementation would integrate with email service
        logger.info(f"Email alert sent: {alert_data['rule_name']}")
    
    async def _send_slack_alert(self, alert_data: Dict[str, Any]):
        """Send Slack alert notification"""
        # Implementation would integrate with Slack API
        logger.info(f"Slack alert sent: {alert_data['rule_name']}")
    
    async def _send_webhook_alert(self, alert_data: Dict[str, Any]):
        """Send webhook alert notification"""
        # Implementation would send HTTP webhook
        logger.info(f"Webhook alert sent: {alert_data['rule_name']}")
    
    def get_metrics(self) -> str:
        """Get Prometheus metrics in text format"""
        return generate_latest(self.metrics.registry)


class HealthChecker:
    """Application health monitoring"""
    
    def __init__(self):
        self.checks: Dict[str, Callable] = {}
        self.register_default_checks()
    
    def register_check(self, name: str, check_func: Callable):
        """Register a health check"""
        self.checks[name] = check_func
    
    def register_default_checks(self):
        """Register default health checks"""
        self.register_check('database', self._check_database)
        self.register_check('redis', self._check_redis)
        self.register_check('disk_space', self._check_disk_space)
        self.register_check('memory', self._check_memory)
    
    async def run_checks(self) -> Dict[str, Any]:
        """Run all health checks"""
        results = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'checks': {}
        }
        
        overall_healthy = True
        
        for name, check_func in self.checks.items():
            try:
                check_result = await check_func()
                results['checks'][name] = check_result
                
                if not check_result.get('healthy', False):
                    overall_healthy = False
                    
            except Exception as e:
                results['checks'][name] = {
                    'healthy': False,
                    'error': str(e),
                    'timestamp': datetime.utcnow().isoformat()
                }
                overall_healthy = False
        
        results['status'] = 'healthy' if overall_healthy else 'unhealthy'
        return results
    
    async def _check_database(self) -> Dict[str, Any]:
        """Check database connectivity"""
        try:
            db = next(get_db())
            result = db.execute(text("SELECT 1"))
            result.scalar()
            
            return {
                'healthy': True,
                'response_time': 0.1,  # Would measure actual response time
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    async def _check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity"""
        try:
            await redis_client.ping()
            
            return {
                'healthy': True,
                'response_time': 0.05,  # Would measure actual response time
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    async def _check_disk_space(self) -> Dict[str, Any]:
        """Check disk space"""
        try:
            disk_usage = psutil.disk_usage('/')
            percent_used = (disk_usage.used / disk_usage.total) * 100
            
            return {
                'healthy': percent_used < 90,
                'percent_used': percent_used,
                'free_gb': disk_usage.free / (1024**3),
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    async def _check_memory(self) -> Dict[str, Any]:
        """Check memory usage"""
        try:
            memory = psutil.virtual_memory()
            
            return {
                'healthy': memory.percent < 90,
                'percent_used': memory.percent,
                'available_gb': memory.available / (1024**3),
                'timestamp': datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }


# Global instances
performance_monitor = PerformanceMonitor()
health_checker = HealthChecker()


# Middleware for automatic monitoring
@asynccontextmanager
async def monitor_request(request: Request):
    """Context manager for request monitoring"""
    start_time = time.time()
    tenant_id = getattr(request.state, 'tenant_id', None)
    
    try:
        yield
    finally:
        duration = time.time() - start_time
        await performance_monitor.track_request(
            method=request.method,
            endpoint=str(request.url.path),
            status_code=200,  # Would get actual status code
            duration=duration,
            tenant_id=tenant_id
        )


def monitor_function(metric_type: str = None):
    """Decorator for monitoring function calls"""
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                
                if metric_type:
                    # Track custom metric
                    await performance_monitor.track_business_metric(
                        metric_type=metric_type,
                        tenant_id=kwargs.get('tenant_id', 'system')
                    )
                
                return result
            except Exception as e:
                await performance_monitor.track_error(
                    error_type=type(e).__name__,
                    tenant_id=kwargs.get('tenant_id'),
                    error_details={'function': func.__name__, 'args': str(args)}
                )
                raise
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                return result
            except Exception as e:
                # Log error for sync functions
                logger.error(f"Error in {func.__name__}: {e}")
                raise
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


# Background task for metrics collection
async def metrics_collector():
    """Background task to collect system metrics"""
    while True:
        try:
            await performance_monitor.update_system_metrics()
            await performance_monitor.check_alerts()
            await asyncio.sleep(60)  # Run every minute
        except Exception as e:
            logger.error(f"Metrics collector error: {e}")
            await asyncio.sleep(60)
