#!/bin/bash

# BiteBase Intelligence - Quick Fix Script
# Fixes common dependency conflicts and ensures consistent package management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔧 BiteBase Intelligence - Quick Fix"
echo "====================================="

# Fix Backend Dependencies
print_status "Fixing backend dependencies..."
cd backend

if [ -d "venv" ]; then
    source venv/bin/activate
    print_status "Installing missing Python packages..."
    pip install geoip2>=4.7.0 folium>=0.14.0
    print_success "Backend dependencies fixed!"
else
    print_warning "Virtual environment not found. Run ./start.sh first."
fi

# Fix Frontend Dependencies
print_status "Fixing frontend dependencies..."
cd ../frontend

# Remove conflicting lock files
print_status "Cleaning up lock file conflicts..."
rm -f yarn.lock

# Clean install with npm
print_status "Reinstalling Node.js dependencies..."
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps

print_success "Frontend dependencies fixed!"

echo ""
print_success "✅ All issues fixed! You can now run:"
echo "   ./start.sh       # Start both services"
echo "   npm run dev      # Frontend only (port 3000)"
echo "   yarn dev         # Frontend only (port 3000)"
