#!/bin/bash

# BiteBase Intelligence - Fixed Startup Script
# Handles port conflicts and webpack errors automatically

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ASCII Art Banner
echo -e "${PURPLE}"
cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║   ██████╗ ██╗████████╗███████╗██████╗  █████╗ ███████╗███████╗║
║   ██╔══██╗██║╚══██╔══╝██╔════╝██╔══██╗██╔══██╗██╔════╝██╔════╝║
║   ██████╔╝██║   ██║   █████╗  ██████╔╝███████║███████╗█████╗  ║
║   ██╔══██╗██║   ██║   ██╔══╝  ██╔══██╗██╔══██║╚════██║██╔══╝  ║
║   ██████╔╝██║   ██║   ███████╗██████╔╝██║  ██║███████║███████╗║
║   ╚═════╝ ╚═╝   ╚═╝   ╚══════╝╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝║
║                                                              ║
║              🍽️  INTELLIGENCE PLATFORM  🚀                   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
EOF
echo -e "${NC}"

# Function to print colored status messages
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to find available port
find_available_port() {
    local start_port=$1
    local port=$start_port
    
    while lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; do
        port=$((port + 1))
    done
    
    echo $port
}

# Function to kill processes on port
kill_port() {
    local port=$1
    local pids=$(lsof -ti :$port 2>/dev/null || true)
    
    if [ ! -z "$pids" ]; then
        print_warning "Killing processes on port $port"
        echo $pids | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
}

# Function to clean build cache
clean_build_cache() {
    print_status "Cleaning build cache to prevent webpack errors..."
    
    if [ -d "frontend/.next" ]; then
        rm -rf frontend/.next
        print_success "Removed frontend/.next"
    fi
    
    if [ -d "frontend/node_modules/.cache" ]; then
        rm -rf frontend/node_modules/.cache
        print_success "Removed frontend cache"
    fi
}

# Cleanup function
cleanup() {
    print_warning "Shutting down services..."
    
    # Kill background processes
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    print_success "Services stopped. Goodbye!"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
print_status "Starting BiteBase Intelligence Platform..."

# Check if we're in the right directory
if [ ! -f "package.json" ] && [ ! -d "frontend" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Clean build cache first
clean_build_cache

# Find available ports
FRONTEND_PORT=$(find_available_port 3000)
BACKEND_PORT=$(find_available_port 8000)

print_status "Using ports: Frontend=$FRONTEND_PORT, Backend=$BACKEND_PORT"

# Start backend
print_status "Starting backend server on http://localhost:$BACKEND_PORT"
cd backend

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment and install dependencies
source venv/bin/activate
pip install -r requirements.txt >/dev/null 2>&1

# Start backend with simplified version
uvicorn app.main_simple:app --host 0.0.0.0 --port $BACKEND_PORT --reload &
BACKEND_PID=$!

cd ..

# Start frontend
print_status "Starting frontend server on http://localhost:$FRONTEND_PORT"
cd frontend

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing frontend dependencies..."
    npm install
fi

# Start frontend
npm run dev -- --port $FRONTEND_PORT &
FRONTEND_PID=$!

cd ..

# Wait for services to start
sleep 5

# Check if services are running
if kill -0 $BACKEND_PID 2>/dev/null && kill -0 $FRONTEND_PID 2>/dev/null; then
    print_success "🎉 All services started successfully!"
    echo ""
    echo -e "${CYAN}📱 Frontend:${NC} http://localhost:$FRONTEND_PORT"
    echo -e "${CYAN}🔧 Backend API:${NC} http://localhost:$BACKEND_PORT"
    echo -e "${CYAN}📚 API Docs:${NC} http://localhost:$BACKEND_PORT/docs"
    echo ""
    print_status "Press Ctrl+C to stop all services"
    
    # Wait for user interrupt
    wait
else
    print_error "Failed to start services"
    cleanup
    exit 1
fi
